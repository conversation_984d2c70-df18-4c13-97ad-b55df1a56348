import axios from "axios";
import { API_ROOT } from "../constants/api";
import { doGetPackage } from "../utils/axiosutisl";

//Mobile เรียกดูรายการ invoice สำหรับ SystemAdmin และ FarmAdmin โดย statusName = waiting, confirm, cancel ส่วน typeName = rent, manage, seed ส่ง minUpdatedAt มา
export const postInvoiceList = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/admin/farm/invoice/list-mobile`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ยืนยัน invoice สำหรับ SystemAdmin และ FarmAdmin กรณีไม่ยืนยันให้ส่ง IsConfirm = false และ description มาด้วย
export const postInvoiceConfirm = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/admin/farm/invoice/confirm`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

//Process Management Screen
//เรียกดูรายการ User Request ใน Process Request ตามสิทธิของ user สำหรับ statusFilter จะมี waiting, in progress, finish ให้ส่งเป็น list string
export const postProcessList = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/admin/farm/process/list-mobile`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//อัพเดตการทำงาน ตามที่ User Request ใน Process Request สำหรับ NewState จะมี In Progress และ finish
export const putProcessUpdate = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/admin/farm/process/update`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียกดูรายละเอียด User Request ใน Process Request โดยใช้ FarmUserPlantingProcessId
export const getProcessDetail = (farmUserPlantingProcessId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/admin/farm/process/detail/${farmUserPlantingProcessId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
// เพิ่ม Comment ใน Process
export const postProcessAdd = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/admin/farm/process-comment/add`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//อัพเดต Comment ใน Process
export const putProcessEditlUpdate = (data) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/admin/farm/process-comment/update`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//ลบ Comment ใน Process
export const deleteProcess = (commentId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/admin/farm/process-comment/delete/${commentId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//แสดง Badge สำหรับ SystemAdmin และ FarmAdmin รายการที่รอการอนุมัติ
export const getFarmbadge = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/admin/farm/badge`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//สดง Badge สำหรับ SystemAdmin และ FarmAdmin รายการที่รอการอนุมัติ
export const getFarmBadge = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/admin/farm/badge`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};

export const getPolicy = () => {
  return axios({
    method: "get",
    url: `${API_ROOT}/admin/homepage/content/list/policy`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
