import axios from "axios";
import { API_ROOT } from "../constants/api";

export const deleteAttachments = (fileId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/realtime/attachments/delete/${fileId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
// PushNotification
export const notiRegister = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/realtime/push-notification/device-token/register`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//Get Push Notification List
export const pushNotiList = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/realtime/push-notification/list`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//Update Read or UnRead Push Notification
export const unReadNoti = (notiId, isRead) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/realtime/push-notification/read/${notiId}/${isRead}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//Delete Push Notification
export const deleteNoti = (deleteId) => {
  return axios({
    method: "delete",
    url: `${API_ROOT}/realtime/push-notification/delete/${deleteId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//นับจำนวน รายการข้อความที่ยังไม่ได้อ่านทั้งหมด โดยส่ง UserId หากเป็นหน้าหลัก farmUserPlotId ใส่ /null ถ้าหน้าฟาร์มให้เอา farmUserPlotId มาใส่
export const pusCount = (userIdLogin, farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/realtime/unread/count/${userIdLogin}/${null}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
export const pusCountFarm = (userIdLogin, farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/realtime/unread/count/${userIdLogin}/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//อัพเดตสถานะ อ่านแล้วทั้งหมด โดยส่ง UserId หากเป็นหน้าหลัก farmUserPlotId ใส่ /null ถ้าหน้าฟาร์มให้เอา farmUserPlotId มาใส่
export const pushReadAll = (userIdLogin, farmUserPlotId) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/realtime/readall/${userIdLogin}/${null}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
export const pushReadFramAll = (userIdLogin, farmUserPlotId) => {
  return axios({
    method: "put",
    url: `${API_ROOT}/realtime/readall/${userIdLogin}/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//เรียก Connection สำหรับ Chat กับ Farm
export const joinChatAdmin = (farmUserPlotId) => {
  return axios({
    method: "get",
    url: `${API_ROOT}/realtime/chat/${farmUserPlotId}`,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
//สร้าง chat ใหม่ เมื่อสร้างเสร็จจะถูกส่งเข้า chat hub สำหรับที่มีการส่งภาพ
export const postAdminMessage = (data) => {
  return axios({
    method: "post",
    url: `${API_ROOT}/realtime/chat/post/message`,
    data,
  })
    .then(function (res) {
      return res.data;
    })
    .catch(function (error) {
      return error;
    });
};
