import React from "react";
import { Header as HeaderRNE } from "@rneui/themed";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
import { StyleSheet, TouchableOpacity, Text, View, Image } from "react-native";
import { scale, verticalScale, moderateScale } from "react-native-size-matters";
import txt from "../../styleSheet/txt";
import Images from "../../utils/imageManager";
//Svg
import { goBack_gay } from "../../assets/svg/svg_naviagte";

export default function Default_Bar({ onBack, title, imagesIcon }: any) {
  const returnPage = () => (
    <TouchableOpacity onPress={onBack}>{goBack_gay()}</TouchableOpacity>
  );
  const centerHeader = () => (
    <Text style={[fonstStyle.f16_bold, txt.txt_606060, { marginTop: 10 }]}>
      {title}
    </Text>
  );
  const rightHeader = () => (
    <Image style={styles.images} source={imagesIcon} resizeMode="contain" />
  );

  return (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={returnPage()}
      centerComponent={centerHeader()}
      rightComponent={rightHeader()}
      // backgroundImage={Images.bgApp}
    />
  );
}

const styles = StyleSheet.create({
  continue: {
    paddingHorizontal: moderateScale(10),
  },
  images: {
    width: 30,
    height: 30,
    marginTop: 10,
    right: 10,
  },
});
