import RNFS from "react-native-fs";
import React, { useEffect, useState } from "react";
import Video from "react-native-video";

const CACHE_DIR = `${RNFS.CachesDirectoryPath}/video-cache`;
const MAX_CACHE_SIZE = 2 * 1024 * 1024 * 1024; // 2GB

async function ensureCacheDirExists() {
  const exists = await RNFS.exists(CACHE_DIR);
  if (!exists) {
    await RNFS.mkdir(CACHE_DIR);
  }
}

function getCacheKeyFromUrl(url: string | null | undefined): string {
  if (!url || typeof url !== "string") return "novideo";
  const path = url.split("?")[0];
  return path.replace(/[^a-zA-Z0-9]/g, "");
}

async function getTotalCacheSize(): Promise<number> {
  const files = await RNFS.readDir(CACHE_DIR);
  return files.reduce((total, file) => total + file.size, 0);
}

async function deleteOldestFilesUntilUnderLimit() {
  const files = await RNFS.readDir(CACHE_DIR);
  const sorted = files.sort((a, b) => {
    const timeA = a.mtime?.getTime() ?? 0;
    const timeB = b.mtime?.getTime() ?? 0;
    return timeA - timeB;
  });

  let total = await getTotalCacheSize();
  for (const file of sorted) {
    if (total <= MAX_CACHE_SIZE) break;
    await RNFS.unlink(file.path);
    total -= file.size;
  }
}

function getVideoExtension(url: string): string {
  if (url.toLowerCase().includes(".mov")) return "mov";
  return "mp4";
}

export async function downloadAndCacheVideo(
  url: string | null | undefined
): Promise<string> {
  if (!url) throw new Error("Video URL is required");
  await ensureCacheDirExists();

  const cacheKey = getCacheKeyFromUrl(url);
  const ext = getVideoExtension(url);
  const cachePath = `${CACHE_DIR}/${cacheKey}.${ext}`;

  const exists = await RNFS.exists(cachePath);
  if (exists) {
    return `file://${cachePath}`;
  }

  try {
    await RNFS.downloadFile({
      fromUrl: url,
      toFile: cachePath,
    }).promise;

    await deleteOldestFilesUntilUnderLimit();
  } catch (err) {
    throw err;
  }

  return `file://${cachePath}`;
}

export const CachedVideo = ({
  videoUrl,
  style,
  isMuted,
  ...props
}: {
  videoUrl: string | null | undefined;
  style?: any;
  isMuted?: boolean;
  [key: string]: any;
}) => {
  const [localPath, setLocalPath] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    if (!videoUrl) return;
    const loadVideo = async () => {
      try {
        const path = await downloadAndCacheVideo(videoUrl);
        if (mounted) setLocalPath(path);
      } catch (error) {
        console.error("Video load failed", error);
      }
    };
    loadVideo();
    return () => {
      mounted = false;
    };
  }, [videoUrl]);

  if (!videoUrl) return null;

  return localPath ? (
    <Video
      source={{ uri: localPath }}
      style={style}
      controls={true}
      resizeMode="cover"
      paused={true}
      muted={isMuted}
      bufferConfig={{
        minBufferMs: 3000,
        maxBufferMs: 10000,
        bufferForPlaybackMs: 1500,
        bufferForPlaybackAfterRebufferMs: 3000,
      }}
      {...props}
    />
  ) : null;
};
