import React from "react";
import { BgColor } from "../../styleSheet/style_Custom";
import {
  View,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import FastImage from "react-native-fast-image";
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;

export default function Loading() {
  const loadingApp: {
    uri: string;
  } = require("../../assets/Images/loading/loadingApp.gif");

  return (
    <View style={[styles.container]}>
      {/* <ActivityIndicator size="large" color={BgColor.Bg_B3DBC0} /> */}
      <FastImage
      style={{
        width: 60,
        height: 60,
        alignItems: "center",
        justifyContent: "center",
      }}
      source={loadingApp}
      resizeMode={FastImage.resizeMode.cover}
    />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "(rgba(0,0,0,0.5))",
  },
});
