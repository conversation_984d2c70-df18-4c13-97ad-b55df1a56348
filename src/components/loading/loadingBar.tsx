import {
    View,
    StyleSheet,
  } from "react-native";
  import React from "react";
  import FastImage from "react-native-fast-image";
  
  export default function LoadingBar() {
    const loadingApp: {
      uri: string;
    } = require("../../assets/Images/loading/loadingApp.gif");
  
    return (
      <View style={[styles.container]}>
      <FastImage
        style={{
          flex: 1,
          width: 40,
          height: 40,
          alignItems: "center",
          justifyContent: "center",
        }}
        source={loadingApp}
        resizeMode={FastImage.resizeMode.cover}
      />
      </View>
    );
  }
  
  const styles = StyleSheet.create({
    container: {
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 9999,
      width: "100%",
      alignItems: "center",
      justifyContent: "center",
    },
  });
  