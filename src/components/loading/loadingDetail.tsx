import { View, Text, StyleSheet } from "react-native";
import React from "react";
import FastImage from "react-native-fast-image";
import { BgOpacity } from "../../styleSheet/style_Custom";
import fonstStyle, { FonstColor, BgColor } from "../../styleSheet/style_Custom";
//Translation
import { useTranslation } from "../../screen/i18n";

export default function LoadingDetail() {
  const { t } = useTranslation();
  const loadingApp: {
    uri: string;
  } = require("../../assets/Images/loading/loadingApp.gif");

  return (
    <View style={[styles.container]}>
      <FastImage
        style={{
          width: 60,
          height: 60,
          alignItems: "center",
          justifyContent: "center",
        }}
        source={loadingApp}
        resizeMode={FastImage.resizeMode.cover}
      />
      <Text style={[fonstStyle.f12_light]}>{t('loading')}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
  },
});
