import React from "react";
import { BgColor } from "../../styleSheet/style_Custom";
import {
  View,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import FastImage from "react-native-fast-image";
const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;

export default function LoadingFarm() {
  const loadMyFarm: {
    uri: string;
  } = require("../../assets/Images/loading/loadMyFarm.gif");

  return (
    <View style={[styles.container]}>
      <FastImage
      style={{
       width: "100%",
       height: "100%",
        alignItems: "center",
        justifyContent: "center",
      }}
      source={loadMyFarm}
      resizeMode={FastImage.resizeMode.cover}
    />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    width: "100%",
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "(rgba(0,0,0,0.5))",
  },
});
