import { View, StyleSheet } from "react-native";
import React from "react";
import FastImage from "react-native-fast-image";
import { BgOpacity } from "../../styleSheet/style_Custom";

export default function LoadingMapDetail() {
  const loadingApp: {
    uri: string;
  } = require("../../assets/Images/loading/loadingApp.gif");

  return (
    <View style={[styles.container]}>
      <FastImage
        style={{
          width: 60,
          height: 60,
          alignItems: "center",
          justifyContent: "center",
        }}
        source={loadingApp}
        resizeMode={FastImage.resizeMode.cover}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    backgroundColor: BgOpacity.Op_0000005
  },
});
