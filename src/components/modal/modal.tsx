import { Text, View, Modal, TouchableOpacity, Linking } from "react-native";
import React from "react";
import { verticalScale, moderateScale } from "react-native-size-matters";
import ImageViewing from "react-native-image-viewing";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle from "../../styleSheet/style_Custom";
//Svg
import { iconError, iconCheck, iconLogout } from "../../assets/svg/svg_other";
//Translation
import { useTranslation } from "../../screen/i18n";

export const ModalPermission = ({ visible, onClose }: any) => {
  const { t } = useTranslation();
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={mod.mod_center}>
        <View style={mod.mod_View}>
          <View style={oth.opt_FlaseLoging}>
            <View style={oth.bg_FlaseCancle}>{iconError()}</View>
          </View>
          <View style={{ bottom: verticalScale(30) }}>
            <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
              {t("permissionCamera")}
            </Text>
            <Text style={[fonstStyle.f12_light, txt.txt_modFlase]}>
              {t("camera_settings")}
            </Text>
          </View>
          <View style={{ flexDirection: "row", bottom: 10 }}>
            <TouchableOpacity style={mod.mod_Cancle} onPress={onClose}>
              <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                {t("replies_cancle")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />
            <TouchableOpacity
              style={mod.mod_Agee}
              onPress={() => {
                Linking.openSettings();
                onClose();
              }}
            >
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                {t("setting")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
export const ModalTrue = ({ visible, onClose }: any) => {
  const { t } = useTranslation();
  return (
    <Modal animationType="fade" transparent={true} visible={visible}>
      <View style={mod.mod_center}>
        <View style={mod.mod_View}>
          <View style={oth.opt_TrueLoging}>
            <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
          </View>
          <View style={{ bottom: verticalScale(30) }}>
            <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
              {t("success_message")}
            </Text>
          </View>
          <TouchableOpacity style={[btn.btn_FlaseLoging]} onPress={onClose}>
            <Text style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}>
              {t("confirm")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
export const ModalFalse = ({ visible, onClose }: any) => {
  const { t } = useTranslation();
  return (
    <Modal animationType="fade" transparent={true} visible={visible}>
      <View style={mod.mod_center}>
        <View style={mod.mod_View}>
          <View style={oth.opt_FlaseLoging}>
            <View style={oth.bg_FlaseLoging}>{iconError()}</View>
          </View>
          <View style={{ bottom: verticalScale(30) }}>
            <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
              {t("please_check")}
            </Text>
          </View>
          <TouchableOpacity style={[btn.btn_FlaseLoging]} onPress={onClose}>
            <Text style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}>
              {t("confirm")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
export const ModalLogOut = ({ visible, onClose, logOut }: any) => {
  const { t } = useTranslation();
  return (
    <Modal animationType="fade" transparent={true} visible={visible}>
      <View style={mod.mod_center}>
        <View style={mod.mod_View}>
          <View style={oth.opt_FlaseLoging}>
            <View style={oth.bg_FlaseLoging}>{iconLogout()}</View>
          </View>
          <View style={{ bottom: verticalScale(30) }}>
            <Text style={[txt.txt_modFlase, fonstStyle.f16_bold]}>
              {t("logout_button")}
            </Text>
          </View>
          <View style={{ flexDirection: "row", bottom: 10 }}>
            <TouchableOpacity style={mod.mod_Cancle} onPress={onClose}>
              <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                {t("replies_cancle")}
              </Text>
            </TouchableOpacity>
            <View style={{ margin: moderateScale(5) }} />
            <TouchableOpacity style={mod.mod_Agee} onPress={logOut}>
              <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                {t("agree")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
export const ModalFullImg = ({ visible, onClose, images }: any) => {
  return (
    <View style={{ flex: 1 }}>
      <ImageViewing
        images={images}
        imageIndex={0}
        visible={visible}
        onRequestClose={onClose}
      />
    </View>
  );
};
export const ModalProfile = ({ visible, onClose, images }: any) => {
  return (
    <View style={{ flex: 1 }}>
      <ImageViewing
        images={images}
        imageIndex={0}
        visible={visible}
        onRequestClose={onClose}
      />
    </View>
  );
};
export const ModalCover = ({ visible, onClose, images }: any) => {
  return (
    <View style={{ flex: 1 }}>
      <ImageViewing
        images={images}
        imageIndex={0}
        visible={visible}
        onRequestClose={onClose}
      />
    </View>
  );
};
