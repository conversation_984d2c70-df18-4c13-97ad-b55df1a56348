import {
  Text,
  View,
  Image,
  Modal,
  FlatList,
  Animated,
  StatusBar,
  TextInput,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import { Badge } from "@rneui/themed";
import ImageViewing from "react-native-image-viewing";
import { useFocusEffect } from "@react-navigation/native";
import React, { useState, useEffect, useRef } from "react";
import { moderateScale } from "react-native-size-matters";
import { createFilter } from "react-native-search-filter";
import AsyncStorage from "@react-native-async-storage/async-storage";
//Style
import ctn from "../../styleSheet/ctn";
import btn from "../../styleSheet/btn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import { goBack_Bg, goBack_x } from "../../assets/svg/svg_naviagte";
import {
  iconCheck,
  iconError,
  iconUpPayment,
  iconDownPayment,
} from "../../assets/svg/svg_other";
//commponents
import Loading from "../../components/loading/loading";
import LoadingApp from "../../components/loading/loadingApp";
import { useOrientation } from "../../hooks/useOrientation";
//Translation
import { useTranslation } from "../i18n";
//Api
import {
  getFarmBadge,
  postProcessList,
  postInvoiceList,
  postInvoiceConfirm,
} from "../../action/Mefarm_Admin_API";
import { getFarmbadge } from "../../action/Mefarm_Admin_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setRequest,
  setDocPayment,
  setLanguageKey,
  setDocProcessList,
} from "../../Redux_Store/action";

export default function AdminPage({ navigation }: any) {
  const orientation = useOrientation();

  const history_approve: {
    uri: string;
  } = require("../../assets/Images/control/history_approve.png");
  const history_time: {
    uri: string;
  } = require("../../assets/Images/control/history_time.png");
  const Ellipse: {
    uri: string;
  } = require("../../assets/Images/control/Ellipse.png");
  const Ellipse2: {
    uri: string;
  } = require("../../assets/Images/control/Ellipse2.png");
  const adminManage: {
    uri: string;
  } = require("../../assets/Images/control/adminManage.png");

  const { t } = useTranslation();
  const inputRef: any = useRef(null);
  //Redux
  const dispatch = useDispatch();
  const docRequest = useSelector((state: any) => state.docRequest);
  const docPayment = useSelector((state: any) => state.docPayment);
  const docLanguageKey = useSelector((state: any) => state.docLanguageKey);
  const docProcessList = useSelector((state: any) => state.docProcessList);
  //Number
  const [activeMenu, setActiveMenu] = useState<any>(1);
  const [activeButton, setActiveButton] = useState<any>(1);
  //Array
  const [docReject, setDocReject] = useState<any>([]);
  const [docApprove, setDocapprove] = useState<any>([]);
  const [invoiceDetail, setInvoiceDetail] = useState<any>([]);
  const [originalList, setOriginalList] = useState<any[]>([]);
  //String
  const [imageUrl, setImageUrl] = useState<string>("");
  const [customerName, setCustomerName] = useState<string>("");
  const [rejectPayment, setRejectPayment] = useState<string>("");
  const [invoiceNumber, setInvoiceNumber] = useState<string>("");
  const [isVerifyRequest, setVerifyRequset] = useState<string>("");
  const [isVerifyPayment, setVerifyPayment] = useState<string>("");
  const [isInvoiceRequestCheck, setInvoiceRequestCheck] = useState<string>("");
  const [isInvoicePaymentCheck, setInvoicePaymentCheck] = useState<string>("");
  //New date
  const [invoiceDate, setInvoiceDate] = useState(new Date());
  //True & False
  const [isModalDetailPayment, setModalDetailPayment] =
    useState<boolean>(false);
  const [isModalDetailRequest, setModalDetailRequest] =
    useState<boolean>(false);
  const [isModalPaymentReject, setModalPaymentReject] =
    useState<boolean>(false);
  const [isModalrejectRequest, setModalRejectRequest] =
    useState<boolean>(false);
  const [isModalrejeckPayment, setModalrejeckPayment] =
    useState<boolean>(false);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isLoadPayment, setLoadPayment] = useState<boolean>(false);
  const [isLoadRequest, setLoadRequest] = useState<boolean>(false);
  const [isLoadProcess, setLoadProcess] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  const [isModalTrueApprove, setModalTrueApprove] = useState<boolean>(false);
  const [isModalTrueRequset, setModalTrueRequset] = useState<boolean>(false);
  const [isModalTruePayment, setModalTruePayment] = useState<boolean>(false);
  //Number
  const [rentCount, setRentCount] = useState<number>(0);
  const [payAmount, setPayAmount] = useState<number>(0);
  const [pageProcess, setProcess] = useState<number>(10);
  const [manageCount, setManageCount] = useState<number>(0);
  const [pageSizePayment, setPageSizePayment] = useState<number>(10);
  const [pageSizeRequest, setPageSizeRequest] = useState<number>(10);
  const [countRequestList, setCountRequestList] = useState<number>(0);
  const [countCheckPayment, setCountCheckPayment] = useState<number>(0);
  //Null
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [openIndexRe, setOpenIndexRe] = useState<number | null>(null);
  const LANGUAGE_KEY = "appLanguage";

  //Function
  useFocusEffect(
    React.useCallback(() => {
      callInvoicePayment();
      callInvoiceRequest();
      callProcessList();
      callFarmBadge();
      callBage();
    }, [dispatch])
  );
  useEffect(() => {
    if (isVerifyRequest === "succeedRequest") {
      setModalDetailRequest(false);
    }
  }, [isVerifyRequest]);
  useEffect(() => {
    if (isVerifyPayment === "succeedPayment") {
      setModalDetailPayment(false);
      // console.log(isVerifyPayment);
    }
  }, [isVerifyPayment]);
  useEffect(() => {
    const lan = async () => {
      const storedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
      // console.log(">>>>", storedLanguage);
      dispatch(setLanguageKey(storedLanguage));
    };
    lan();
  }, []);
  const callInvoicePayment = async () => {
    try {
      setLoadPayment(true);
      const req = {
        searchText: "",
        pageSize: pageSizePayment,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        statusName: "waiting",
        typeName: "rent",
      };
      const res = await postInvoiceList(req);
      const data = res.model || "";
      // console.log(JSON.stringify(res.model, null, 2));
      dispatch(setDocPayment(data));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadPayment(false);
    }
  };
  const callInvoiceRequest = async () => {
    try {
      setLoadRequest(true);
      const req = {
        searchText: "",
        pageSize: pageSizeRequest,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        statusName: "waiting",
        typeName: "manage",
      };
      const res = await postInvoiceList(req);
      const data = res.model || "";
      // console.log(JSON.stringify(res, null, 2));
      dispatch(setRequest(data));
    } catch (error) {
      console.log(error);
    } finally {
      setLoadRequest(false);
    }
  };
  const callInvoiceConfirmPayment = async () => {
    setModalTrueApprove(false);
    setOpenIndex(null);
    setOpenIndexRe(null);
    try {
      setLoadIng(true);
      // const req = docApprove;
      const req = {
        ...docApprove,
        isConfirm: true,
        // cancelRemarkTh: rejectPayment,
      };
      const res = await postInvoiceConfirm(req);

      if (res) {
        // อัปเดต docPayment ใน Redux
        dispatch(setDocPayment(res));
        dispatch(setRequest(res));
        callInvoicePayment();
        callInvoiceRequest();
        callBage();
      }
      // console.log(JSON.stringify(res, null, 2));
    } catch (error) {
      console.error("เกิดข้อผิดพลาด:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const callInvoiceRejectPayment = async () => {
    setRejectPayment("");
    setOpenIndex(null);
    setOpenIndexRe(null);
    setModalPaymentReject(false);
    setModalrejeckPayment(false);
    setModalRejectRequest(false);
    try {
      setLoadIng(true);
      const req = {
        ...docReject,
        isConfirm: false,
        cancelRemarkTh: rejectPayment,
      };
      const res = await postInvoiceConfirm(req);

      if (res) {
        dispatch(setDocPayment(res));
        dispatch(setRequest(res));
        callInvoicePayment();
        callInvoiceRequest();
        callBage();
      }
      // console.log(JSON.stringify(req, null, 2));
    } catch (error) {
      console.error("เกิดข้อผิดพลาด:", error);
    } finally {
      setLoadIng(false);
    }
  };
  const callProcessList = async () => {
    try {
      setLoadProcess(true);
      const res = {
        searchText: "",
        pageSize: pageProcess,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        farmId: "",
        statusFilter: ["waiting", "in progress"],
        userId: "",
      };
      const req = await postProcessList(res);
      dispatch(setDocProcessList(req.model));
      setOriginalList(req.model);
      // console.log(JSON.stringify(req.model, null, 2));
    } catch (error) {
    } finally {
      setLoadProcess(false);
    }
  };
  const callFarmBadge = async () => {
    try {
      setLoadProcess(true);
      const req = await getFarmBadge();
      // console.log(JSON.stringify(req, null, 2));
    } catch (error) {
    } finally {
      setLoadProcess(false);
    }
  };
  const callBage = async () => {
    try {
      const res = await getFarmbadge();

      const data = res.model || "";
      // console.log(data);
      const manageCount = data.invoiceManageCount || 0;
      const rentCount = data.invoiceRentCount || 0;
      const seedCount = data.invoiceSeedCount || 0;
      const requestCount = data.manageRequestCount || 0;

      const checkPayment = manageCount + rentCount;
      setRentCount(rentCount);
      setManageCount(manageCount);
      setCountCheckPayment(checkPayment);
      setCountRequestList(requestCount);
      // console.log(">>>", allCount);
      // setAllBange(allCount)
    } catch (error) {
      console.log(error);
    }
  };

  //Filter
  const searchUpdated = (text: string) => {
    if (text === "") {
      // ถ้าช่อง search ว่าง กลับไปใช้ต้นฉบับ
      dispatch(setDocProcessList(originalList));
    } else {
      const filtered = originalList.filter(
        createFilter(text, ["farmUserPlotNameTh", "farmUserPlotNameEn"])
      );
      dispatch(setDocProcessList(filtered));
    }
  };

  //Hand
  const handleButtonClick = (buttonIndex: any) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };
  const handleButtonMenu = (buttonIndex: any) => {
    if (activeMenu === buttonIndex) {
      setActiveMenu(null);
    } else {
      setActiveMenu(buttonIndex);
    }
  };

  //Load
  const loadMoreDataPeyment = async () => {
    setLoadPayment(true);
    setPageSizePayment((prev) => prev + 10);
    await callInvoicePayment();
    setLoadPayment(false);
  };
  const loadMoreDataRequest = async () => {
    setLoadRequest(true);
    setPageSizeRequest((prev) => prev + 10);
    await callInvoiceRequest();
    setLoadRequest(false);
  };
  const loadMoreDataProcess = async () => {
    setLoadProcess(true);
    setProcess((prev) => prev + 10);
    await callProcessList();
    setLoadProcess(false);
  };

  //On
  const onSlider = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  const onSliderRe = (index: number) => {
    setOpenIndexRe(openIndexRe === index ? null : index);
  };
  const onApprove = (item: any) => {
    // console.log(JSON.stringify(item, null, 2));
    setDocapprove(item);
    setModalTrueApprove(true);
  };
  const onVerifyRequset = () => {
    setModalTrueRequset(true);
  };
  const onVerifyPayment = () => {
    setModalTruePayment(true);
  };
  const onRejectPayment = () => {
    setModalrejeckPayment(true);
  };

  //Close
  const closeApprove = () => {
    setModalTrueApprove(false);
  };
  const closeReject = () => {
    setModalrejeckPayment(false);
  };
  const closeVerifyRequset = () => {
    setModalTrueRequset(false);
  };
  const closeVerifyPayment = () => {
    setModalTruePayment(false);
  };

  //Goto
  const goCheckPayment = (item: any) => {
    setVerifyPayment("");
    setModalDetailPayment(true);
    setInvoiceNumber(item.invoiceNumber);
    setInvoiceDate(item.invoiceDate);
    setCustomerName(item.customerName);
    setInvoiceDetail(item.invoiceDetail);
    setPayAmount(item.payAmount);
    setImageUrl(item.imageUrl);
  };
  const goCheckRequest = (item: any) => {
    setVerifyRequset("");
    setModalDetailRequest(true);
    setInvoiceNumber(item.invoiceNumber);
    setInvoiceDate(item.invoiceDate);
    setCustomerName(item.customerName);
    setInvoiceDetail(item.invoiceDetail);
    setPayAmount(item.payAmount);
    setImageUrl(item.imageUrl);
  };
  const goPaymentReject = (item: any) => {
    setVerifyPayment("");
    setModalPaymentReject(true);
    setDocReject(item);
    setInvoiceNumber(item.invoiceNumber);
    setInvoiceDate(item.invoiceDate);
    setCustomerName(item.customerName);
    setInvoiceDetail(item.invoiceDetail);
    setPayAmount(item.payAmount);
    setImageUrl(item.imageUrl);
  };
  const goRequestReject = (item: any) => {
    setVerifyPayment("");
    setModalRejectRequest(true);
    setDocReject(item);
    setInvoiceNumber(item.invoiceNumber);
    setInvoiceDate(item.invoiceDate);
    setCustomerName(item.customerName);
    setInvoiceDetail(item.invoiceDetail);
    setPayAmount(item.payAmount);
    setImageUrl(item.imageUrl);
  };
  const goFullImg = () => {
    setModalFullImg(true);
  };
  const goVerifyPayment = () => {
    const succeedPayment = "succeedPayment";
    setVerifyPayment(succeedPayment);
    setInvoicePaymentCheck(invoiceNumber);
    setModalTruePayment(false);
  };
  const goVerifyRequset = () => {
    const succeedRequest = "succeedRequest";
    setVerifyRequset(succeedRequest);
    setInvoiceRequestCheck(invoiceNumber);
    setModalTrueRequset(false);
  };
  const goDetilManage = (item: any) => {
    navigation.navigate("DetailProcess", { itemManage: item });
  };

  //Ui
  const header = () => {
    return (
      <View style={ctn.ctn_headerManage}>
        {/* {Hearder Title} */}
        <View
          style={
            orientation === "portrait"
              ? ctn.ctn_headerTitle
              : ctn.ctn_headerTitleLan
          }
        >
          <View style={ctn.ctn_spaceBet}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{ bottom: 10, zIndex: 999 }}
            >
              {goBack_Bg()}
            </TouchableOpacity>
            <Text style={[fonstStyle.f16_bold, txt.txt_white, { zIndex: 999 }]}>
              {t("manage_farm")}
            </Text>
            {activeButton === 1 ? (
              <TouchableOpacity
                style={{ paddingHorizontal: moderateScale(10) }}
                onPress={() => navigation.navigate("HistoryPayRequest")}
              >
                <Image
                  style={{ width: 24, height: 24 }}
                  source={history_approve}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={{ paddingHorizontal: moderateScale(10) }}
                onPress={() => navigation.navigate("HistoryManage")}
              >
                <Image
                  style={{ width: 24, height: 24 }}
                  source={history_time}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* {Select Menu Header} */}
        <View
          style={{
            zIndex: 999,
            paddingHorizontal: moderateScale(40),
            marginTop: moderateScale(10),
          }}
        >
          <View style={ctn.ctn_selectManage}>
            <View style={{ flexDirection: "row" }}>
              {/* {Tap 1} */}
              <View style={{ width: "50%" }}>
                <TouchableOpacity
                  style={[
                    activeButton === 1
                      ? oth.cardTapManage
                      : oth.cardTapManageNon,
                  ]}
                  onPress={() => handleButtonClick(1)}
                  disabled={activeButton === 1 ? true : false}
                >
                  {countCheckPayment != 0 && (
                    <Badge
                      value={countCheckPayment}
                      status="error"
                      badgeStyle={oth.bageNotiAdminTap}
                    />
                  )}
                  <Image
                    style={img.img_iconEllipse}
                    source={Ellipse}
                    resizeMode="cover"
                  />

                  <Text
                    style={[
                      fonstStyle.f14_bold,
                      activeButton === 1 ? txt.txt_white : txt.txt_606060,
                      { textAlign: "center" },
                    ]}
                  >
                    {t("check_payment")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* {Tap 2} */}
              <View style={{ width: "50%" }}>
                <TouchableOpacity
                  onPress={() => handleButtonClick(2)}
                  disabled={activeButton === 2 ? true : false}
                  style={[
                    activeButton === 2
                      ? oth.cardTapManage2
                      : oth.cardTapManageNon2,
                  ]}
                >
                  {countRequestList != 0 && (
                    <Badge
                      value={countRequestList}
                      status="error"
                      badgeStyle={oth.bageNotiAdminTap}
                    />
                  )}
                  <Image
                    style={img.img_iconEllipse2}
                    source={Ellipse2}
                    resizeMode="cover"
                  />

                  <Text
                    style={[
                      fonstStyle.f14_bold,
                      activeButton === 2 ? txt.txt_white : txt.txt_606060,
                      { textAlign: "center" },
                    ]}
                  >
                    {t("request_list")}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* {BgStyle} */}
        <View style={oth.bgStyles1} />
        <View style={oth.bgStyles2} />
        <View style={oth.bgStyles3}>
          <View style={oth.bgStyles4} />
        </View>
        <View style={oth.bgStyles5} />
        <View style={oth.bgStyles6} />
        <View style={{ margin: moderateScale(10) }} />
      </View>
    );
  };
  const goBackPayment = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            paddingHorizontal: moderateScale(20),
            marginTop: moderateScale(15),
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {invoiceNumber}
          </Text>
        </View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => setModalDetailPayment(false)}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const goBackRequest = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            paddingHorizontal: moderateScale(20),
            marginTop: moderateScale(15),
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {invoiceNumber}
          </Text>
        </View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => setModalDetailRequest(false)}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const goBackPaymentReject = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            paddingHorizontal: moderateScale(20),
            marginTop: moderateScale(15),
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {invoiceNumber}
          </Text>
        </View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => setModalPaymentReject(false)}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const goBackRequestReject = () => {
    return (
      <View style={ctn.ctn_goBackPayandRe}>
        <View
          style={{
            paddingHorizontal: moderateScale(20),
            marginTop: moderateScale(15),
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {invoiceNumber}
          </Text>
        </View>

        <View
          style={{
            paddingHorizontal: moderateScale(10),
          }}
        >
          <TouchableOpacity onPress={() => setModalRejectRequest(false)}>
            {goBack_x()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const selectMenu = () => {
    return (
      <View style={{ backgroundColor: BgColor.Bg_FFFFFF }}>
        <View style={ctn.ctn_selectManageMenu}>
          <View style={{ flexDirection: "row" }}>
            {/* {Tap 1} */}
            <View style={{ width: "50%" }}>
              <TouchableOpacity
                style={[
                  activeMenu === 1 ? oth.cardTapMenu : oth.cardTapMenuNon,
                ]}
                onPress={() => handleButtonMenu(1)}
                disabled={activeMenu === 1 ? true : false}
              >
                {rentCount != 0 && (
                  <Badge
                    value={rentCount}
                    status="error"
                    badgeStyle={oth.bageNotiAdmin}
                  />
                )}

                <Image
                  style={img.img_iconEllipse}
                  source={Ellipse}
                  resizeMode="cover"
                />

                <Text
                  style={[
                    fonstStyle.f12_bold,
                    txt.txt_346359,
                    { textAlign: "center" },
                  ]}
                >
                  {t("check_payment")}
                </Text>
              </TouchableOpacity>
            </View>

            {/* {Tap 2} */}
            <View style={{ width: "50%" }}>
              <TouchableOpacity
                onPress={() => handleButtonMenu(2)}
                disabled={activeMenu === 2 ? true : false}
                style={[
                  activeMenu === 2 ? oth.cardTapMenu2 : oth.cardTapMenuNon2,
                ]}
              >
                {manageCount != 0 && (
                  <Badge
                    value={manageCount}
                    status="error"
                    badgeStyle={oth.bageNotiAdmin}
                  />
                )}
                <Image
                  style={img.img_iconEllipse2}
                  source={Ellipse2}
                  resizeMode="cover"
                />

                <Text
                  style={[
                    fonstStyle.f12_bold,
                    txt.txt_346359,
                    { textAlign: "center" },
                  ]}
                >
                  {t("request_list")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    );
  };
  const content = () => {
    return (
      <View style={{ flex: 3 }}>
        {activeMenu === 1 && (
          <FlatList
            data={docPayment}
            removeClippedSubviews={false}
            style={{ backgroundColor: BgColor.Bg_EEF5F1 }}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) =>
              item.id?.toString() || index.toString()
            }
            renderItem={({ item, index }) => renderPayment({ item, index })}
            ListEmptyComponent={
              <View style={{ alignItems: "center", padding: 10 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                  {t("noData")}
                </Text>
              </View>
            }
            ListFooterComponent={
              <>
                <View style={{ margin: moderateScale(20) }} />
                {isLoadPayment ? LoadingApp() : null}
                <View style={{ margin: moderateScale(200) }} />
              </>
            }
            onScroll={({ nativeEvent }) => {
              const { layoutMeasurement, contentOffset, contentSize } =
                nativeEvent;
              const isScrolledToEnd =
                layoutMeasurement.height + contentOffset.y >=
                contentSize.height - 20;

              if (isScrolledToEnd && !isLoadPayment) {
                loadMoreDataPeyment();
              }
            }}
          />
        )}
        {activeMenu === 2 && (
          <FlatList
            data={docRequest}
            removeClippedSubviews={false}
            style={{ backgroundColor: BgColor.Bg_EEF5F1 }}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) =>
              item.id?.toString() || index.toString()
            }
            renderItem={({ item, index }) => renderRequest({ item, index })}
            ListEmptyComponent={
              <View style={{ alignItems: "center", padding: 10 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                  {t("noData")}
                </Text>
              </View>
            }
            ListFooterComponent={
              <>
                <View style={{ margin: moderateScale(20) }} />
                {isLoadRequest ? LoadingApp() : null}
                <View style={{ margin: moderateScale(200) }} />
              </>
            }
            onScroll={({ nativeEvent }) => {
              const { layoutMeasurement, contentOffset, contentSize } =
                nativeEvent;
              const isScrolledToEnd =
                layoutMeasurement.height + contentOffset.y >=
                contentSize.height - 20;

              if (isScrolledToEnd && !isLoadRequest) {
                loadMoreDataRequest();
              }
            }}
          />
        )}
      </View>
    );
  };
  const contentListManage = () => {
    return (
      <View style={ctn.continueMain}>
        <View
          style={{
            paddingHorizontal: moderateScale(20),
            marginTop: moderateScale(10),
          }}
        >
          <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
            {t("Todo_list")}
          </Text>
        </View>

        {/* {ค้นหา} */}
        <View style={oth.continueSearchManage}>
          <View style={oth.contentSearchManage}>
            <TextInput
              style={[fonstStyle.f12_light, txt.txt_606060]}
              placeholder={t("search")}
              onChangeText={(text) => searchUpdated(text)}
            />
          </View>
        </View>

        {/* รายการ */}
        <View
          style={{
            paddingHorizontal: moderateScale(10),
            marginTop: moderateScale(20),
          }}
        >
          <View style={ctn.ctn_titleHeaderManage}>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("product")}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {t("order")}
            </Text>

            <View style={{ flexDirection: "row" }}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("status")}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("manage")}
              </Text>
            </View>
          </View>

          <FlatList
            data={docProcessList}
            removeClippedSubviews={false}
            style={{ borderWidth: 2, borderColor: BgColor.Bg_F4F4F4 }}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) =>
              item.id?.toString() || index.toString()
            }
            renderItem={({ item, index }) => renderListManage({ item, index })}
            ListEmptyComponent={
              <View style={{ alignItems: "center", padding: 10 }}>
                <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                  {t("noData")}
                </Text>
              </View>
            }
            ListFooterComponent={
              <>
                <View style={{ margin: moderateScale(20) }} />
                {isLoadProcess ? LoadingApp() : null}
                <View style={{ margin: moderateScale(200) }} />
              </>
            }
            onScroll={({ nativeEvent }) => {
              const { layoutMeasurement, contentOffset, contentSize } =
                nativeEvent;
              const isScrolledToEnd =
                layoutMeasurement.height + contentOffset.y >=
                contentSize.height - 10;

              if (isScrolledToEnd && !isLoadProcess) {
                loadMoreDataProcess();
              }
            }}
          />
        </View>
      </View>
    );
  };
  const renderListManage = ({ item, index }: any) => {
    return (
      <>
        <View
          style={{
            marginTop: moderateScale(10),
            paddingHorizontal: moderateScale(10),
          }}
        >
          <View style={ctn.ctn_listManage}>
            <Text
              style={[fonstStyle.f14_medium, txt.txt_606060, { width: "20%" }]}
              numberOfLines={1}
            >
              {item.farmUserPlotNameTh}
            </Text>

            <View style={{ flexDirection: "column", width: "35%" }}>
              {item.processTypeNameTh != "" && (
                <Text
                  style={[
                    fonstStyle.f14_medium,
                    txt.txt_606060,
                    { textAlign: "center" },
                  ]}
                  numberOfLines={1}
                >
                  {item.processTypeNameTh}
                </Text>
              )}

              {item.processTypeNameEn != "" && (
                <Text
                  style={[
                    fonstStyle.f14_medium,
                    txt.txt_606060,
                    { textAlign: "center" },
                  ]}
                  numberOfLines={1}
                >
                  {item.processTypeNameEn}
                </Text>
              )}
            </View>

            <View
              style={
                item.currentState === "Waiting"
                  ? oth.cardStatusManage
                  : oth.cardStatusManageNew
              }
            >
              <Text
                style={[
                  fonstStyle.f14_medium,
                  item.currentState === "Waiting"
                    ? txt.txt_red
                    : txt.txt_4FA5F4,
                ]}
                numberOfLines={1}
              >
                {item.currentState === "Waiting" ? t("new") : t("issue")}
              </Text>
            </View>

            <TouchableOpacity
              style={btn.btn_btnManage}
              onPress={() => goDetilManage(item)}
            >
              <Image
                style={{ width: 20, height: 20 }}
                source={adminManage}
                resizeMode="cover"
              />
            </TouchableOpacity>
          </View>
        </View>
        <View style={oth.line_profile} />
      </>
    );
  };
  const renderPayment = ({ item, index }: any) => {
    return (
      <View style={{ marginTop: moderateScale(5) }}>
        <View style={oth.cardApprove}>
          <View style={[ctn.ctn_spaceBet, { width: "100%" }]}>
            <View style={{ flexDirection: "row" }}>
              <View
                style={[
                  oth.bgStatus,
                  {
                    backgroundColor:
                      item.isPackageRenewal === false
                        ? BgColor.Bg_E3F2FF
                        : BgColor.Bg_FFE1C3,
                  },
                ]}
              >
                <Text
                  style={[
                    fonstStyle.f12_light,
                    item.isPackageRenewal === false
                      ? txt.txt_4FA5F4
                      : txt.txt_orange,
                  ]}
                >
                  {item.isPackageRenewal === false ? t("new") : t("renew")}
                </Text>
              </View>
              <View style={{ margin: moderateScale(2) }} />

              <View style={[oth.bgStatusArea]}>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {t("rental_fee")}
                </Text>
              </View>
            </View>

            <View style={[oth.bgStatusPackage, { width: "50%" }]}>
              {item.invoiceDetail.map((data: any) => (
                <Text
                  style={[fonstStyle.f12_light, txt.txt_6AB252]}
                  numberOfLines={1}
                >
                  {data.description}
                </Text>
              ))}
            </View>
          </View>

          <View style={{ margin: moderateScale(5) }} />
          <View style={oth.line_profile} />
          <View style={{ margin: moderateScale(5) }} />
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text
              style={[fonstStyle.f14_bold, txt.txt_606060, { width: "50%" }]}
              numberOfLines={1}
            >
              {item.customerName}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.invoiceNumber}
            </Text>
          </View>

          <View style={{ margin: moderateScale(2) }} />
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {moment(item.invoiceDate)
                .locale(t("locale"))
                .format("DD MMMM YYYY HH:mm")}
            </Text>
            <View style={{ flexDirection: "column" }}>
              <Text style={[fonstStyle.f18_bold, txt.txt_606060]}>
                ฿ {item.invoiceAmount}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              {openIndex != index && (
                <TouchableOpacity
                  onPress={() => onSlider(index)}
                  style={{
                    paddingHorizontal: moderateScale(10),
                    paddingVertical: moderateScale(2),
                    borderRadius: 180,
                    backgroundColor: BgColor.Bg_84B8A2,
                    alignItems: "center",
                  }}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {iconDownPayment()}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {openIndex === index && (
          <>
            <View style={oth.cardSliderApprove}>
              <View
                style={[
                  ctn.ctn_spaceBet,
                  {
                    justifyContent:
                      isInvoicePaymentCheck != item.invoiceNumber
                        ? "flex-end"
                        : "space-between",
                  },
                ]}
              >
                {/* Check */}
                <TouchableOpacity
                  style={btn.btn_checkApprove}
                  onPress={() => goCheckPayment(item)}
                >
                  <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                    {t("check")}
                  </Text>
                </TouchableOpacity>

                {isInvoicePaymentCheck === item.invoiceNumber ? (
                  <>
                    {/* Approve */}
                    <TouchableOpacity
                      disabled={isInvoicePaymentCheck != item.invoiceNumber}
                      onPress={() => onApprove(item)}
                      style={[btn.btn_btnApprove]}
                    >
                      <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                        {t("approve")}
                      </Text>
                    </TouchableOpacity>

                    {/* No Approve */}
                    <TouchableOpacity
                      style={btn.btn_btnNoApprove}
                      onPress={() => goPaymentReject(item)}
                    >
                      <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                        {t("not_approved")}
                      </Text>
                    </TouchableOpacity>
                  </>
                ) : null}
              </View>
            </View>

            {openIndex === index && (
              <View style={{ alignItems: "center" }}>
                <TouchableOpacity
                  onPress={() => onSlider(index)}
                  style={{
                    width: "15%",
                    borderRadius: 180,
                    marginTop: -30,
                    backgroundColor: BgColor.Bg_D6D6D6,
                    alignItems: "center",
                  }}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {iconUpPayment()}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </View>
    );
  };
  const renderRequest = ({ item, index }: any) => {
    return (
      <View style={{ marginTop: moderateScale(5) }}>
        <View style={oth.cardApprove}>
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <View style={{ flexDirection: "row" }}>
              <View
                style={[
                  oth.bgStatus,
                  {
                    backgroundColor:
                      item.isPackageRenewal === false
                        ? BgColor.Bg_E3F2FF
                        : BgColor.Bg_FFE1C3,
                  },
                ]}
              >
                <Text
                  style={[
                    fonstStyle.f12_light,
                    item.isPackageRenewal === false
                      ? txt.txt_4FA5F4
                      : txt.txt_orange,
                  ]}
                >
                  {item.isPackageRenewal === false ? t("new") : t("renew")}
                </Text>
              </View>
              <View style={{ margin: moderateScale(2) }} />

              <View style={[oth.bgStatusArea]}>
                <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                  {t("farm_costs")}
                </Text>
              </View>
            </View>
          </View>
          <View style={{ margin: moderateScale(5) }} />
          <View style={oth.line_profile} />
          <View style={{ margin: moderateScale(5) }} />
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text
              style={[fonstStyle.f14_bold, txt.txt_606060, { width: "50%" }]}
              numberOfLines={1}
            >
              {item.customerName}
            </Text>
            <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
              {item.invoiceNumber}
            </Text>
          </View>
          <View style={{ margin: moderateScale(2) }} />

          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
              {moment(item.invoiceDate)
                .locale(t("locale"))
                .format("DD MMMM YYYY HH:mm")}
            </Text>
            <View style={{ flexDirection: "column" }}>
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {item.invoiceDetail.length} {t("list")}
              </Text>
              <Text style={[fonstStyle.f18_bold, txt.txt_606060]}>
                ฿ {item.invoiceAmount}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              {openIndexRe != index && (
                <TouchableOpacity
                  onPress={() => onSliderRe(index)}
                  style={{
                    paddingHorizontal: moderateScale(10),
                    paddingVertical: moderateScale(2),
                    borderRadius: 180,
                    backgroundColor: BgColor.Bg_84B8A2,
                    alignItems: "center",
                  }}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {iconDownPayment()}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        {openIndexRe === index && (
          <>
            <View style={oth.cardSliderApprove}>
              <View
                style={[
                  ctn.ctn_spaceBet,
                  {
                    justifyContent:
                      isInvoiceRequestCheck != item.invoiceNumber
                        ? "flex-end"
                        : "space-between",
                  },
                ]}
              >
                {/* Check */}
                <TouchableOpacity
                  style={btn.btn_checkApprove}
                  onPress={() => goCheckRequest(item)}
                >
                  <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                    {t("check")}
                  </Text>
                </TouchableOpacity>

                {isInvoiceRequestCheck === item.invoiceNumber ? (
                  <>
                    {/* Approve */}
                    <TouchableOpacity
                      disabled={isInvoiceRequestCheck != item.invoiceNumber}
                      onPress={() => onApprove(item)}
                      style={[
                        isInvoiceRequestCheck === item.invoiceNumber
                          ? btn.btn_btnApprove
                          : btn.btn_btnApproveNon,
                      ]}
                    >
                      <Text
                        style={[
                          fonstStyle.f12_light,
                          isInvoiceRequestCheck === item.invoiceNumber
                            ? txt.txt_white
                            : txt.txt_606060,
                        ]}
                      >
                        {t("approve")}
                      </Text>
                    </TouchableOpacity>

                    {/* No Approve */}
                    <TouchableOpacity
                      style={btn.btn_btnNoApprove}
                      onPress={() => goRequestReject(item)}
                    >
                      <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                        {t("not_approved")}
                      </Text>
                    </TouchableOpacity>
                  </>
                ) : null}
              </View>
            </View>

            {openIndexRe === index && (
              <View style={{ alignItems: "center" }}>
                <TouchableOpacity
                  onPress={() => onSliderRe(index)}
                  style={{
                    width: "15%",
                    borderRadius: 180,
                    marginTop: -30,
                    backgroundColor: BgColor.Bg_D6D6D6,
                    alignItems: "center",
                  }}
                >
                  <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                    {iconUpPayment()}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </View>
    );
  };
  const modalPayment = () => {
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={isModalDetailPayment}
      >
        <>
          {goBackPayment()}
          <ScrollView style={ctn.continueMain}>
            {/* Content */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {moment(invoiceDate)
                  .locale(t("locale"))
                  .format("DD MMMM YYYY HH:mm")}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                {t("tenant")}
              </Text>
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {customerName}
              </Text>
              <View style={{ margin: moderateScale(10) }} />
              {invoiceDetail.map((data: any, index: number) => (
                <View style={ctn.ctn_spaceBet}>
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {data.description}
                  </Text>

                  <Text
                    style={[
                      fonstStyle.f14_light,
                      data.netPrice === 0 ? txt.txt_orange : txt.txt_606060,
                    ]}
                  >
                    ฿ {data.netPrice === 0 ? t("free") : data.netPrice}
                  </Text>
                </View>
              ))}
              <View style={{ margin: moderateScale(10) }} />
              <View style={ctn.ctn_spaceEnd}>
                <Text
                  style={[
                    fonstStyle.f16_bold,
                    txt.txt_606060,
                    { textAlign: "right" },
                  ]}
                >
                  {t("total_payment")}
                </Text>
                <View style={{ margin: moderateScale(5) }} />
                <Text style={[fonstStyle.f16_bold, txt.txt_green]}>
                  ฿ {payAmount}
                </Text>
              </View>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_orange]}>
                {t("please_check_payment")}
              </Text>
              {/* image */}
              <TouchableOpacity
                style={{ alignItems: "center" }}
                onPress={() => goFullImg()}
              >
                <Image
                  style={[img.img_bankUpload, { borderRadius: 20 }]}
                  source={{ uri: imageUrl }}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>
            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              style={[btn.btn_detailPayandRe]}
              onPress={() => goVerifyPayment()}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_editSave]}>
                {t("confirm_ver")}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      </Modal>
    );
  };
  const modalRequest = () => {
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={isModalDetailRequest}
      >
        <>
          {goBackRequest()}
          <ScrollView style={ctn.continueMain}>
            {/* Content */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                {customerName}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <View style={oth.line_profile} />
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {moment(invoiceDate)
                  .locale(t("locale"))
                  .format("DD MMMM YYYY HH:mm")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("list")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              {invoiceDetail.map((data: any, index: number) => (
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {index + 1}. {data.description}
                  </Text>

                  <View style={{ flexDirection: "row" }}>
                    <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                      {data.quantity} x
                    </Text>
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f14_light,
                        data.netPrice === 0 ? txt.txt_orange : txt.txt_606060,
                      ]}
                    >
                      ฿ {data.netPrice === 0 ? t("free") : data.netPrice}
                    </Text>
                  </View>
                </View>
              ))}
              <View style={{ margin: moderateScale(5) }} />
              <Text
                style={[
                  fonstStyle.f16_bold,
                  txt.txt_606060,
                  { textAlign: "right" },
                ]}
              >
                {t("total_orders")} ฿ {payAmount}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_orange]}>
                {t("please_check_payment")}
              </Text>
              {/* image */}
              <TouchableOpacity
                style={{ alignItems: "center" }}
                onPress={() => goFullImg()}
              >
                <Image
                  style={[img.img_bankUpload, { borderRadius: 20 }]}
                  source={{ uri: imageUrl }}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>
            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              style={[btn.btn_detailPayandRe]}
              onPress={() => goVerifyRequset()}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_editSave]}>
                {t("confirm_ver")}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      </Modal>
    );
  };
  const modalPaymentReject = () => {
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={isModalPaymentReject}
      >
        <>
          {goBackPaymentReject()}
          <ScrollView style={ctn.continueMain}>
            {/* Content */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                {customerName}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <View style={oth.line_profile} />
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {moment(invoiceDate)
                  .locale(t("locale"))
                  .format("DD MMMM YYYY HH:mm")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("list")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              {invoiceDetail.map((data: any, index: number) => (
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {index + 1}. {data.description}
                  </Text>

                  <View style={{ flexDirection: "row" }}>
                    <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                      {data.quantity} x
                    </Text>
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f14_light,
                        data.netPrice === 0 ? txt.txt_orange : txt.txt_606060,
                      ]}
                    >
                      ฿ {data.netPrice === 0 ? t("free") : data.netPrice}
                    </Text>
                  </View>
                </View>
              ))}
              <View style={{ margin: moderateScale(5) }} />
              <Text
                style={[
                  fonstStyle.f16_bold,
                  txt.txt_606060,
                  { textAlign: "right" },
                ]}
              >
                {t("total_orders")} ฿ {payAmount}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_orange]}>
                {t("please_cancellation")}
              </Text>
              {/* comment */}
              <View style={{ margin: moderateScale(5) }} />
              <TextInput
                style={[
                  txt.txt_inputReject,
                  fonstStyle.f12_light,
                  txt.txt_606060,
                ]}
                ref={inputRef}
                placeholder={t("post_something")}
                onChangeText={setRejectPayment}
                value={rejectPayment}
                textAlignVertical="top"
                autoFocus={true}
                editable
                multiline
              />
              {/* image */}
              <TouchableOpacity
                style={{ alignItems: "center" }}
                onPress={() => goFullImg()}
              >
                <Image
                  style={[img.img_bankUpload, { borderRadius: 20 }]}
                  source={{ uri: imageUrl }}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>

            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              disabled={rejectPayment === ""}
              style={[
                rejectPayment === ""
                  ? btn.btn_detailPayandReNon
                  : btn.btn_detailPayandRe,
              ]}
              onPress={() => onRejectPayment()}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_editSave]}>
                {t("confirm_cancel")}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      </Modal>
    );
  };
  const modalRequestReject = () => {
    return (
      <Modal
        animationType="slide"
        transparent={true}
        visible={isModalrejectRequest}
      >
        <>
          {goBackRequestReject()}
          <ScrollView style={ctn.continueMain}>
            {/* Content */}
            <View
              style={{
                marginTop: moderateScale(10),
                paddingHorizontal: moderateScale(20),
              }}
            >
              <Text style={[fonstStyle.f16_bold, txt.txt_606060]}>
                {customerName}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <View style={oth.line_profile} />
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                {moment(invoiceDate)
                  .locale(t("locale"))
                  .format("DD MMMM YYYY HH:mm")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("list")}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              {invoiceDetail.map((data: any, index: number) => (
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                    {index + 1}. {data.description}
                  </Text>

                  <View style={{ flexDirection: "row" }}>
                    <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                      {data.quantity} x
                    </Text>
                    <View style={{ margin: moderateScale(5) }} />
                    <Text
                      style={[
                        fonstStyle.f14_light,
                        data.netPrice === 0 ? txt.txt_orange : txt.txt_606060,
                      ]}
                    >
                      ฿ {data.netPrice === 0 ? t("free") : data.netPrice}
                    </Text>
                  </View>
                </View>
              ))}
              <View style={{ margin: moderateScale(5) }} />
              <Text
                style={[
                  fonstStyle.f16_bold,
                  txt.txt_606060,
                  { textAlign: "right" },
                ]}
              >
                {t("total_orders")} ฿ {payAmount}
              </Text>
              <View style={{ margin: moderateScale(5) }} />
              <Text style={[fonstStyle.f14_light, txt.txt_orange]}>
                {t("please_cancellation")}
              </Text>
              {/* comment */}
              <View style={{ margin: moderateScale(5) }} />
              <TextInput
                style={[
                  txt.txt_inputReject,
                  fonstStyle.f12_light,
                  txt.txt_606060,
                ]}
                ref={inputRef}
                placeholder={t("post_something")}
                onChangeText={setRejectPayment}
                value={rejectPayment}
                textAlignVertical="top"
                autoFocus={true}
                editable
                multiline
              />
              {/* image */}
              <TouchableOpacity
                style={{ alignItems: "center" }}
                onPress={() => goFullImg()}
              >
                <Image
                  style={[img.img_bankUpload, { borderRadius: 20 }]}
                  source={{ uri: imageUrl }}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            </View>
            <View style={{ margin: moderateScale(120) }} />
          </ScrollView>
          <View style={{ alignItems: "center" }}>
            <TouchableOpacity
              disabled={rejectPayment === ""}
              style={[
                rejectPayment === ""
                  ? btn.btn_detailPayandReNon
                  : btn.btn_detailPayandRe,
              ]}
              onPress={() => onRejectPayment()}
            >
              <Text style={[fonstStyle.f14_bold, txt.txt_editSave]}>
                {t("confirm_cancel")}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      </Modal>
    );
  };
  const modalFullImg = () => {
    return (
      <ImageViewing
        images={[{ uri: imageUrl }]}
        imageIndex={0}
        visible={isModalFullImg}
        onRequestClose={() => setModalFullImg(false)}
      />
    );
  };
  const alertVerifyRequest = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalTrueRequset}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_ver")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => closeVerifyRequset()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => goVerifyRequset()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertVerifyPayment = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalTruePayment}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_ver")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => closeVerifyPayment()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => goVerifyPayment()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertApprove = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalTrueApprove}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_TrueLoging}>
              <View style={oth.bg_TrueLoging}>{iconCheck()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modSuccess, fonstStyle.f14_bold]}>
                {t("confirm_approval")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => closeApprove()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonAgree]}
                onPress={() => callInvoiceConfirmPayment()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const alertReject = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalrejeckPayment}
        style={{ zIndex: 999 }}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseLoging}>
              <View style={oth.bg_FlaseLoging}>{iconError()}</View>
            </View>
            <View style={{ bottom: moderateScale(30) }}>
              <Text style={[txt.txt_modReject, fonstStyle.f14_bold]}>
                {t("confirm_reject")}
              </Text>
            </View>

            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={[btn.btn_bottonCancle]}
                onPress={() => closeReject()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_606060]}
                >
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={[btn.btn_bottonReject]}
                onPress={() => callInvoiceRejectPayment()}
              >
                <Text
                  style={[txt.txt_center, fonstStyle.f12_bold, txt.txt_white]}
                >
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        {isLoadIng ? <Loading /> : null}
        <View style={ctn.continueMain}>
          {header()}
          {activeButton === 1 && selectMenu()}
          {activeButton === 1 && content()}
          {activeButton === 2 && contentListManage()}
          {modalPayment()}
          {modalRequest()}
          {modalFullImg()}
          {alertApprove()}
          {alertReject()}
          {alertVerifyRequest()}
          {alertVerifyPayment()}
          {modalPaymentReject()}
          {modalRequestReject()}
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        {isLoadIng ? <Loading /> : null}
        <View style={ctn.continueMain}>
          {header()}
          {activeButton === 1 && selectMenu()}
          {activeButton === 1 && content()}
          {activeButton === 2 && contentListManage()}
          {modalPayment()}
          {modalRequest()}
          {modalFullImg()}
          {alertApprove()}
          {alertReject()}
          {alertVerifyRequest()}
          {alertVerifyPayment()}
          {modalPaymentReject()}
          {modalRequestReject()}
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle={"dark-content"} hidden={false} />
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
