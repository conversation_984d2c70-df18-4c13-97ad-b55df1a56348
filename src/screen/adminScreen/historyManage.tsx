import {
  Text,
  View,
  Image,
  StatusBar,
  TextInput,
  ScrollView,
  FlatList,
  TouchableOpacity,
} from "react-native";
import moment from "moment";
import React, { useState } from "react";
import { Header as HeaderRNE } from "@rneui/themed";
import { useFocusEffect } from "@react-navigation/native";
import { moderateScale } from "react-native-size-matters";
import { createFilter } from "react-native-search-filter";
//Style
import ctn from "../../styleSheet/ctn";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import btn from "../../styleSheet/btn";
import { goBack_gay } from "../../assets/svg/svg_naviagte";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//commponents
import LoadingApp from "../../components/loading/loadingApp";
//Translation
import { useTranslation } from "../i18n";
//Api
import { postProcessList } from "../../action/Mefarm_Admin_API";
//Redux
import { useDispatch, useSelector } from "react-redux";
import { setDocHistoryProcess } from "../../Redux_Store/action";

export default function HistoryManage({ navigation }: any) {
  const adminManage: {
    uri: string;
  } = require("../../assets/Images/control/adminManage.png");

  const { t } = useTranslation();
  //Redux
  const dispatch = useDispatch();
  const docProcessHistory = useSelector(
    (state: any) => state.docProcessHistory
  );
  //Array
  const [filter, setfilter] = useState<any>([]);
  //True & False
  const [isLoadProcess, setLoadProcess] = useState<boolean>(false);
  //Number
  const [pageProcess, setProcess] = useState<number>(10);

  //Function
  useFocusEffect(
    React.useCallback(() => {
      callProcessList();
    }, [])
  );
  const callProcessList = async () => {
    try {
      setLoadProcess(true);
      const res = {
        searchText: "",
        pageSize: pageProcess,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        farmId: "",
        statusFilter: ["finish"],
        userId: "",
      };
      const req = await postProcessList(res);
      dispatch(setDocHistoryProcess(req.model));
      setfilter(req.model);
      console.log(JSON.stringify(req.model, null, 2));
    } catch (error) {
    } finally {
      setLoadProcess(false);
    }
  };

  //Load
  const loadMoreDataProcess = async () => {
    setLoadProcess(true);
    setProcess((prev) => prev + 10);
    await callProcessList();
    setLoadProcess(false);
  };

  //Filter
  const searchUpdated = (text: string) => {
    const filter = docProcessHistory.filter(
      createFilter(text, ["farmUserPlotNameTh", "farmUserPlotNameEn"])
    );
    setfilter(filter);
  };

  //Goto
  const goBack = () => {
    navigation.goBack();
  };
  const goDetilManage = (item: any) => {
    navigation.navigate("DetailProcess", { itemManage: item });
  };

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={centerComment()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>{goBack_gay()}</TouchableOpacity>
  );
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("history")}
    </Text>
  );
  const content = () => {
    return (
      <>
        <View style={{ flex: 1, marginTop: moderateScale(10) }}>
          {/* {Search} */}
          <View style={oth.continueSearchHistory}>
            <View style={oth.contentSearchHistory}>
              <TextInput
                style={[fonstStyle.f12_light, txt.txt_606060]}
                placeholder={t("search")}
                onChangeText={searchUpdated}
              />
            </View>
          </View>

          <View
            style={{
              paddingHorizontal: moderateScale(20),
              marginTop: moderateScale(10),
            }}
          >
            <View style={ctn.ctn_titleHeaderManage}>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("product")}
              </Text>
              <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                {t("order")}
              </Text>

              <View style={{ flexDirection: "row" }}>
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {t("status")}
                </Text>
                <View style={{ margin: moderateScale(10) }} />
                <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
                  {t("manage")}
                </Text>
              </View>
            </View>

            <FlatList
              data={filter}
              removeClippedSubviews={false}
              style={{ borderWidth: 2, borderColor: BgColor.Bg_F4F4F4 }}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) =>
                item.id?.toString() || index.toString()
              }
              renderItem={({ item, index }) =>
                renderListManage({ item, index })
              }
              ListEmptyComponent={
                <View style={{ alignItems: "center", padding: 10 }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_gray]}>
                    {t("noData")}
                  </Text>
                </View>
              }
              ListFooterComponent={
                <>
                  <View style={{ margin: moderateScale(20) }} />
                  {isLoadProcess ? LoadingApp() : null}
                  <View style={{ margin: moderateScale(200) }} />
                </>
              }
            />
          </View>
        </View>
      </>
    );
  };
  const renderListManage = ({ item, index }: any) => {
    return (
      <>
        <View
          style={{
            marginTop: moderateScale(10),
            paddingHorizontal: moderateScale(10),
          }}
        >
          <View style={ctn.ctn_listManage}>
            <Text
              style={[fonstStyle.f14_medium, txt.txt_606060, { width: "20%" }]}
              numberOfLines={1}
            >
              {item.farmUserPlotNameTh}
            </Text>

            <Text
              style={[fonstStyle.f14_medium, txt.txt_606060, { width: "35%" }]}
              numberOfLines={1}
            >
              {item.processTypeNameTh}
            </Text>

            <View style={oth.cardStatusSuccess}>
              <Text
                style={[fonstStyle.f14_medium, txt.txt_6AB252]}
                numberOfLines={1}
              >
                {t("finish")}
              </Text>
            </View>

            <TouchableOpacity
              style={btn.btn_btnManage}
              onPress={() => goDetilManage(item)}
            >
              <Image
                style={{ width: 20, height: 20 }}
                source={adminManage}
                resizeMode="cover"
              />
            </TouchableOpacity>
          </View>
        </View>
        <View style={oth.line_profile} />
      </>
    );
  };

  return (
    <>
      {/* {isLoadIng ? <Loading /> : null} */}
      <StatusBar barStyle={"dark-content"} hidden={false} />
      <View style={ctn.continueMain}>
        {headerBar()}
        {content()}
      </View>
    </>
  );
}
