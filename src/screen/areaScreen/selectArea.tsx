import {
  View,
  Text,
  Image,
  FlatList,
  TextInput,
  SafeAreaView,
  NativeScrollEvent,
  TouchableOpacity,
  NativeSyntheticEvent,
} from "react-native";
import moment from "moment";
import { Header as HeaderRNE } from "@rneui/themed";
import { moderateScale } from "react-native-size-matters";
import React, { useEffect, useState } from "react";
//StyleSheet
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import fonstStyle, { BgColor, FonstColor } from "../../styleSheet/style_Custom";
//Svg
import { goBack_BgGreen } from "../../assets/svg/svg_naviagte";
import { iconNextArea, iconSearchArea } from "../../assets/svg/svg_other";
//Components
import Images from "../../utils/imageManager";
import { useOrientation } from "../../hooks/useOrientation";
import LoadingApp from "../../components/loading/loadingApp";
//Api
import { getFarmApi } from "../../action/Mefarm_Farm_API";
//Translation
import { useTranslation } from "../i18n";
//Redux
import { setListFram, setSelectedFarm } from "./../../Redux_Store/action";
import { useDispatch, useSelector } from "react-redux";

export default function SelectArea({ navigation, route }: any) {
  const orientation = useOrientation();
  const imgNoArea: {
    uri: string;
  } = require("../../assets/Images/package/imgOnArea.png");
  const { t } = useTranslation();
  const [isSeadArea, setSeadArea] = useState<string>("");
  const [isLoadPosts, setLoadPosts] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState<number>(10);
  //Redux
  const dispatch = useDispatch();
  const docListFram = useSelector((state: any) => state.docListFram);

  //Function
  useEffect(() => {
    callMyFarm();
  }, [pageSize]);
  const callMyFarm = async () => {
    try {
      setLoadPosts(true);
      const req = {
        searchText: isSeadArea,
        pageSize: pageSize,
        minUpdatedAt: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      };
      const response = await getFarmApi(req);
      const farmArea = response.model || "";
      dispatch(setListFram(farmArea));
    } catch (error) {
      console.error("Unexpected error:", error);
    } finally {
      setLoadPosts(false);
    }
  };
  const handleSearchTextChange = (text: string) => {
    setSeadArea(text);
  };
  const handleSearch = () => {
    callMyFarm();
  };
  const convertRaiToSquareMeters = (rai: number): number => {
    const squareMetersPerRai = 1600;
    return rai * squareMetersPerRai;
  };
  const goDetail_ = async (item: any) => {
    dispatch(setSelectedFarm(item));
    navigation.navigate("DetailArea");
  };
   const goDetail = async (item: any) => {
    navigation.navigate("DetailArea", {
      docFarm: item,
    });
  };
  const goBack = () => {
    navigation.navigate("Bottom_Tab", {
      screen: "PlusArea",
    });
  };
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;

    const isScrolledToEnd =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (isScrolledToEnd && !isLoadPosts) {
      // console.log("ScrollView reached the end, loading more...");
      loadMoreData();
    }
  };
  const loadMoreData = async () => {
    setLoadPosts(true);

    setPageSize((prev) => prev + 10);
    await callMyFarm();
  };
  const flasListArea = () => (
    <FlatList
      data={docListFram}
      contentContainerStyle={{ justifyContent: "center" }}
      renderItem={renderArea}
      nestedScrollEnabled={true}
      keyExtractor={(item, index) => index.toString()}
      numColumns={2}
      onScroll={handleScroll}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={() => (
        <>
          <View style={{ margin: moderateScale(20) }} />
          {isLoadPosts ? LoadingApp() : null}
          <View style={{ margin: moderateScale(120) }} />
        </>
      )}
    />
  );

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
       backgroundImage={Images.bgApp}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>
      <View>{goBack_BgGreen()}</View>
    </TouchableOpacity>
  );
  const titalHeader = () => {
    return (
      <>
        <View style={{ margin: moderateScale(5) }} />
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>{t("select")}</Text>
        <Text style={[fonstStyle.f14_bold, txt.txt_606060]}>
          {t("Cultivation_area")}
        </Text>
      </>
    );
  };
  const sendArea = () => {
    return (
      <View style={{ paddingBottom: moderateScale(10) }}>
        <View style={[oth.cardSead_area]}>
          <TextInput
            style={[fonstStyle.f12_light, txt.txt_InputSend]}
            placeholder={t("Find_Area")}
            placeholderTextColor={FonstColor.Tc_6A938D}
            onChangeText={handleSearchTextChange}
            value={isSeadArea}
          />
          <TouchableOpacity style={img.img_iconSearc} onPress={handleSearch}>
            {iconSearchArea()}
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  const renderArea = ({ item }: any) => {
    const rai = item.rai;
    const squareMeters = convertRaiToSquareMeters(rai);
    return (
      <>
        <View style={[oth.animated_selectArea]}>
          <View style={ctn.ctn_selectArea}>
            <Image
              style={[
                item.imageUrl ? img.img_selectArea : img.img_selectAreaNon,
              ]}
              source={item.imageUrl ? { uri: item.imageUrl } : imgNoArea}
              resizeMode="cover"
            />

            <View style={oth.card_detailArea}>
              <View style={{ flexDirection: "column" }}>
                <Text
                  numberOfLines={2}
                  style={[fonstStyle.f12_bold, txt.txt_detailArea]}
                >
                  {item.farmName}
                </Text>
                <Text style={[fonstStyle.f10_light, txt.txt_detailArea]}>
                  {item.provinceName}
                </Text>
                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                    {t("area")}{" "}
                  </Text>
                  <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                    {item.rai}
                  </Text>
                  <Text style={[fonstStyle.f12_light, txt.txt_white]}>
                    {" "}
                    {t("rai")}
                  </Text>
                </View>
                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f10_bold, txt.txt_606060]}>
                    {squareMeters}{" "}
                  </Text>
                  <Text style={[fonstStyle.f10_light, txt.txt_white]}>
                    {t("Square_Meters")}
                  </Text>
                </View>
              </View>

              {/* {Next} */}
              <View style={ctn.ctn_nextArea}>
                <TouchableOpacity
                  style={oth.card_nextArea}
                  onPress={() => goDetail(item)}
                >
                  {iconNextArea()}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <View style={ctn.continueMain}>
          <View style={{ paddingHorizontal: moderateScale(20) }}>
            {titalHeader()}
            {sendArea()}
            {flasListArea()}
          </View>
        </View>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView
        style={[ctn.continue, { backgroundColor: BgColor.Bg_F4F4F4 }]}
      >
        <View style={ctn.continueMain}>
          <View style={{ paddingHorizontal: moderateScale(20) }}>
            {titalHeader()}
            {sendArea()}
            {flasListArea()}
          </View>
        </View>
      </SafeAreaView>
    );
  };

  return (
    <>
      {headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
