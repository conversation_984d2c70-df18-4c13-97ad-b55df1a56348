import {
  Text,
  View,
  FlatList,
  Platform,
  StatusBar,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
} from "react-native";
import moment from "moment";
import RNFS from "react-native-fs";
import Images from "../../utils/imageManager";
import { moderateScale } from "react-native-size-matters";
import React, { useRef, useState, useEffect } from "react";
import Default_Bar from "../../components/appBar/default_Bar";
import { request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { launchImageLibrary, launchCamera } from "react-native-image-picker";
import {
  LogLevel,
  HubConnection,
  HubConnectionBuilder,
} from "@microsoft/signalr";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, {
  BgColor,
  BorderColor,
  FonstSize,
} from "../../styleSheet/style_Custom";
//Svg
import {
  iconSend,
  iconVideo,
  iconCamera,
  iconRightChat,
  iconSendDisble,
  iconGalleryChat,
} from "../../assets/svg/svg_other";
//Redux
import {
  setFarmUserPlotId,
  setJoinRoomAdmin,
} from "./../../Redux_Store/action";
import { useDispatch, useSelector } from "react-redux";
//Api
import { WEBSOCKET_CHAT } from "../../constants/api";
import {
  joinChatAdmin,
  postAdminMessage,
} from "../../action/Mefarm_Realtime_API";
import { aiGetToken } from "../../action/Mefarm_Ai_API";
//Translation
import { useTranslation } from "../../screen/i18n";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import { CachedVideo } from "../../components/cacheFiles/cacheVideo";
import { ModalFullImg } from "../../components/modal/modal";
import LoadingApp from "../../components/loading/loadingApp";
import { WEBSOCKET_AI } from "../../constants/api";

export default function ChatAi({ navigation, route }: any) {
  const { t } = useTranslation();
  const inputRef: any = useRef(null);
  const flatListRef: any = useRef(null);
  //Redux
  const dispatch = useDispatch();
  const farmUserPlotId = useSelector((state: any) => state.farmUserPlotId);
  const joinRoomAdminState = useSelector((state: any) => state.joinRoomAdmin);
  //Route
  const params = route.params || "";
  const type = params.type || "";
  //State
  const [roomId, setRoomId] = useState("");
  const [accessToken, setAccessToken] = useState("");
  const [message, setMessage] = useState<string>("");

  const [roomAdmin, setRoomAdmin] = useState<any>({});
  const [imageUris, setImageUris] = useState<any>([]);
  const [fileNames, setFileNames] = useState<any>([]);
  const [typeNames, setTypeNames] = useState<any>([]);
  const [imageFull, setImageFull] = useState<any>([]);
  const [videoUris, setVideoUris] = useState<any>([]);
  const [adminMessage, setAdminMessage] = useState<any>([]);
  const [videoFileNames, setVideoFileNames] = useState<any>([]);
  const [videoTypeNames, setVideoTypeNames] = useState<any>([]);
  const [adminLoadMessage, setAdminLoadMessage] = useState<any>([]);

  const [connection, setConnection] = useState<null | HubConnection>(null);
  const [isShowMenu, setShowMenu] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  interface AiMessage {
    id: string;
    type: "ai" | "user";
    message: {
      text: string;
      image: string[];
    };
    created_at: string;
    message_type: number;
    sender_user_id: string;
  }
  const [aiMessages, setAiMessages] = useState<AiMessage[]>([]);
  const [aiLoadMessage, setAiLoadMessage] = useState<AiMessage[]>([]);
  const [tempMessages, setTempMessages] = useState<AiMessage[]>([]); // ข้อความชั่วคราวที่ส่งไปแล้ว
  const [isAiLoading, setIsAiLoading] = useState<boolean>(false);

  const isImageUrl = (url: string) => {
    return /\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/i.test(url);
  };
  const isVideoUrl = (url: string) => {
    return /\.(mp4|mov|avi|mkv|webm|ogg)(\?.*)?$/i.test(url);
  };

  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    if (type === "Admin") {
      roadData();
    } else {
      roadDataAi();
    }
    requestCameraPermission();
  }, [accessToken, roomId]);
  useEffect(() => {
    let isMounted = true;
    let newConnection: HubConnection | null = null;

    const initializeConnection = async () => {
      newConnection = new HubConnectionBuilder()
        .withUrl(WEBSOCKET_CHAT)
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build();

      newConnection.on("ReceiveMessage", (msg) => {
        setAdminMessage((prev: any) => [...prev, msg]);
        // console.log("ReceiveMessage", msg);
      });
      newConnection.on("ReceiveLoadMoreMessage", (msg) => {
        setAdminLoadMessage((prev: any) => [...prev, ...msg]);
        // console.log(JSON.stringify(msg, null, 2));
      });

      newConnection.onreconnected(() => {
        if (isMounted && roomAdmin?.roomId) {
          joinRoom(newConnection!);
        }
      });

      try {
        await newConnection.start();
        setConnection(newConnection);
      } catch (err) {
        console.error("Failed to start SignalR connection:", err);
      }
    };

    initializeConnection();

    return () => {
      isMounted = false;
      if (newConnection) {
        newConnection.stop();
      }
    };
  }, []);
  useEffect(() => {
    if (connection && connection.state === "Connected" && roomAdmin?.roomId) {
      joinRoom(connection);
    }
  }, [connection, roomAdmin.roomId]);
  useEffect(() => {
    const socketUrl = `${WEBSOCKET_AI}?access_token=${accessToken}&room_id=${roomId}`;
    socketRef.current = new WebSocket(socketUrl);

    socketRef.current.onopen = () => {
      console.log("WebSocket connected");

      if (type !== "Admin") {
        getMessageAi();
      }
    };

    socketRef.current.onmessage = (event) => {
      try {
        const response = JSON.parse(event.data);
        console.log("WebSocket response:", JSON.stringify(response, null, 2));

        if (response.type === "getmessage" && response.data && Array.isArray(response.data)) {
          // ถ้าเป็นการโหลดข้อความเก่า (getmessage)
          setAiLoadMessage(response.data);
        } else if (response.type === "ai_response" && response.message) {
          // ถ้าเป็นข้อความตอบกลับจาก AI
          const aiMessage: AiMessage = {
            id: response.timestamp || Date.now().toString(),
            type: "ai",
            message: {
              text: response.message, // ใช้ response.message โดยตรง
              image: [],
            },
            created_at: response.timestamp || new Date().toISOString(),
            message_type: 2,
            sender_user_id: "ai",
          };

          // ย้ายข้อความจาก tempMessages ไปยัง aiLoadMessage
          setAiLoadMessage((prev) => {
            // รวมข้อความจาก tempMessages และเพิ่ม AI response
            const tempMessagesData = tempMessages;
            const allMessages = [...prev, ...tempMessagesData, aiMessage];

            // กรองข้อความซ้ำ
            const uniqueMessages = allMessages.filter(
              (msg, idx, arr) => arr.findIndex((m) => m.id === msg.id) === idx
            );

            console.log("Moving temp messages to aiLoadMessage:", uniqueMessages.length);
            return uniqueMessages;
          });

          // ล้าง tempMessages หลังจากย้ายแล้ว
          setTempMessages([]);

          // ปิด loading เมื่อได้รับข้อความตอบกลับจาก AI
          setIsAiLoading(false);
        } else if (response.data && Array.isArray(response.data)) {
          // กรณีที่ไม่มี type แต่มี data เป็น array (fallback)
          setAiLoadMessage(response.data);
        }
      } catch (error) {
        console.error("Invalid JSON:", event.data);
        setIsAiLoading(false);
      }
    };

    socketRef.current.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    socketRef.current.onclose = () => {
      console.log("WebSocket closed");
    };

    return () => {
      socketRef.current?.close();
    };
  }, [type, accessToken, roomId]);

  // Auto scroll to bottom when new AI message arrives
  useEffect(() => {
    console.log("aiLoadMessage updated:", aiLoadMessage.length, aiLoadMessage);
    if (flatListRef.current && aiLoadMessage.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [aiLoadMessage]);

  const joinRoom = async (conn: HubConnection) => {
    if (!conn || conn.state !== "Connected") {
      return;
    }
    if (!roomAdmin?.roomId) {
      return;
    }
    const joinData = {
      roomId: roomAdmin.roomId,
      roomName: roomAdmin.roomName,
      roomType: roomAdmin.roomType,
      senderUserId: roomAdmin.senderUserId,
      accessToken: roomAdmin.accessToken,
      receiverUserId: roomAdmin.receiverUserId,
      isDescending: roomAdmin.isDescending,
      firstPageSize: roomAdmin.firstPageSize,
    };
    try {
      await conn.invoke("JoinRoom", joinData);
    } catch (err) {
      // console.error("Error invoking JoinRoom:", err);
    }
  };
  const roadData = async () => {
    try {
      const res = await joinChatAdmin(farmUserPlotId);
      const joinRoomAdmin = res.model || {};
      // console.log(JSON.stringify(res, null, 2));
      // dispatch(setJoinRoomAdmin(joinRoomAdmin));
      setRoomAdmin(joinRoomAdmin);
    } catch (error) {
      console.log(error);
    }
  };
  const roadDataAi = async () => {
    try {
      const req = {
        FarmUserPlotId: farmUserPlotId,
      };
      const res = await aiGetToken(req);
      const joinRoomAi = res.model || {};
      // console.log(JSON.stringify(joinRoomAi, null, 2));

      joinRoomAi.map((data: any) => {
        const accesstoken = data.access_token || "";
        const roomid = data.room_id || "";
        // console.log(roomid);

        setAccessToken(accesstoken);
        setRoomId(roomid);
      });
    } catch (error) {
      console.log(error);
    }
  };
  const requestCameraPermission = async () => {
    try {
      let result;
      if (Platform.OS === "android") {
        result = await request(PERMISSIONS.ANDROID.CAMERA);
      } else {
        // result = await request(PERMISSIONS.IOS.CAMERA);
      }

      if (result === RESULTS.GRANTED) {
        console.log("📸 Camera permission granted");
      } else {
        // Alert.alert("❌ Camera permission denied");
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    }
  };
  const goBack = () => {
    navigation.goBack();
  };
  const goFullImg = (item: any) => {
    setImageFull(item.message);
    setModalFullImg(true);
  };
  const convertImageToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting image to Base64: ", error);
      return "";
    }
  };
  const convertVideoToBase64 = async (uri: string) => {
    try {
      const base64 = await RNFS.readFile(uri, "base64");
      return base64;
    } catch (error) {
      console.error("Error converting video to Base64: ", error);
      return "";
    }
  };
  const openCamera = () => {
    const options: any = {
      mediaType: "photo",
      saveToPhotos: true,
      quality: 1,
    };

    launchCamera(options, async (response: any) => {
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }

        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);

        await callSendImages(newImageUris, newFileNames, newTypeNames);
      }
    });
  };
  const callSendImages = async (
    imageUris: string[],
    fileNames: string[],
    typeNames: string[]
  ) => {
    try {
      const imageContent = await Promise.all(
        imageUris.map(async (uri: any, index: any) => {
          const base64Data = await convertImageToBase64(uri);
          return {
            fileName: fileNames[index] || "",
            contentType: typeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );
      const videoContent = await Promise.all(
        videoUris.map(async (uri: any, index: any) => {
          const base64Data = await convertVideoToBase64(uri);
          return {
            fileName: videoFileNames[index] || "",
            contentType: videoTypeNames[index] || "",
            base64Data: base64Data,
          };
        })
      );
      const req = {
        connection: {
          roomId: roomAdmin.roomId,
          roomName: roomAdmin.roomName,
          roomType: roomAdmin.roomType,
          senderUserId: roomAdmin.senderUserId,
          accessToken: roomAdmin.accessToken,
          receiverUserId: roomAdmin.receiverUserId,
          isDescending: roomAdmin.isDescending,
          firstPageSize: roomAdmin.firstPageSize,
        },
        messageContent: message,
        imageContent: imageContent,
        videoContent: videoContent,
      };
      const res = await postAdminMessage(req);
      console.log(JSON.stringify(res, null, 2));
      // console.log(JSON.stringify(req, null, 2));
    } catch (error) {
      console.log(error);
    } finally {
    }
  };
  const selectImageFromLibrary = () => {
    launchImageLibrary({ mediaType: "photo" }, async (response: any) => {
      // console.log("response: ", response);
      if (!response.didCancel) {
        let newImageUris: string[] = [];
        let newFileNames: string[] = [];
        let newTypeNames: string[] = [];

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newImageUris.push(asset.uri);
            newFileNames.push(asset.fileName);
            newTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newImageUris.push(response.uri);
          newFileNames.push(response.fileName);
          newTypeNames.push(response.type);
        }
        setImageUris((prev: any) => [...prev, ...newImageUris]);
        setFileNames((prev: any) => [...prev, ...newFileNames]);
        setTypeNames((prev: any) => [...prev, ...newTypeNames]);

        await callSendImages(newImageUris, newFileNames, newTypeNames);
      }
    });
  };
  const selectVideoFromLibrary = () => {
    launchImageLibrary({ mediaType: "video" }, async (response: any) => {
      if (!response.didCancel) {
        let newVideoUris: string[] = [];
        let newVideoFileNames: string[] = [];
        let newVideoTypeNames: string[] = [];
        // console.log(response);

        if (response.assets) {
          response.assets.forEach((asset: any) => {
            newVideoUris.push(asset.uri);
            newVideoFileNames.push(asset.fileName);
            newVideoTypeNames.push(asset.type);
          });
        } else if (response.uri) {
          newVideoUris.push(response.uri);
          newVideoFileNames.push(response.fileName);
          newVideoTypeNames.push(response.type);
        }
        setVideoUris(newVideoUris);
        setVideoFileNames(newVideoFileNames);
        setVideoTypeNames(newVideoTypeNames);

        await callSendImages(
          newVideoUris,
          newVideoFileNames,
          newVideoTypeNames
        );
      }
    });
  };
  const callAdminMessage = async () => {
    try {
      if (connection) {
        await connection.invoke("SendMessage", message, 1);
        setMessage("");
      }
    } catch (error) {
      console.log(error);
    } finally {
      // if (connection) joinRoom(connection);
    }
  };
  const callAiMessage = async () => {
    try {
      const originalText = message; // เก็บข้อความต้นฉบับ

      const userMessage: AiMessage = {
        id: Date.now().toString(),
        type: "user",
        message: {
          text: originalText, // ใช้ originalText โดยตรง
          image: [],
        },
        created_at: new Date().toISOString(),
        message_type: 1,
        sender_user_id: "user",
      };

      console.log("Adding user message:", userMessage);

      // เพิ่มข้อความของผู้ใช้เข้าไปใน tempMessages เพื่อแสดงทันที
      setTempMessages((prev) => [...prev, userMessage]);

      // ล้างข้อความใน input
      setMessage("");

      const payload = {
        type: "message",
        prompt: message,
        image_data_list: [],
        streaming: false,
      };

      if (
        socketRef.current &&
        socketRef.current.readyState === WebSocket.OPEN
      ) {
        // แสดง loading สำหรับการตอบกลับ
        setIsAiLoading(true);
        console.log("Setting loading to true");

        socketRef.current.send(JSON.stringify(payload));
        console.log("Sent:", payload);
      } else {
        console.warn("WebSocket is not open");
      }

      setMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      setIsAiLoading(false);
    }
  };
  const getMessageAi = async () => {
    try {
      const payload = {
        type: "getmessage",
        page: 1,
        page_size: 20,
      };

      if (
        socketRef.current &&
        socketRef.current.readyState === WebSocket.OPEN
      ) {
        socketRef.current.send(JSON.stringify(payload));
        console.log("Sent getmessage:", payload);
      } else {
        console.warn("WebSocket is not open");
      }
    } catch (error) {
      console.error("Error requesting messages:", error);
    }
  };

  const renderChat = ({ item }: any) => {
    return (
      <>
        <View style={[styles.bubbleContainer, styles.bubbleRight]}>
          <View style={{ flexDirection: "column", alignItems: "flex-end" }}>
            <View style={[styles.bubble, styles.bubbleMe]}>
              {/* ข้อความปกติ */}
              {!isImageUrl(item.message) && !isVideoUrl(item.message) && (
                <Text style={fonstStyle.f12_light}>{item.message}</Text>
              )}

              {/* รูปภาพ */}
              {isImageUrl(item.message) && (
                <TouchableOpacity onPress={() => goFullImg(item)}>
                  <MyImageComponent
                    imageUrl={item.message}
                    style={styles.image}
                  />
                </TouchableOpacity>
              )}

              {/* วิดีโอ */}
              {isVideoUrl(item.message) && (
                <CachedVideo videoUrl={item.message} style={styles.video} />
              )}
            </View>

            <Text style={[fonstStyle.f8_light, txt.txt_gray]}>
              {moment(item.messageTime).format("DD/MM/YYYY")}
            </Text>
          </View>
        </View>
      </>
    );
  };
  const renderAi = ({ item }: { item: any }) => {
    const { type, message } = item;

    let text = "";
    if (typeof message.text === "string") {
      // สำหรับข้อความใน tempMessages ใช้ originalText โดยตรง
      // สำหรับข้อความใน aiLoadMessage ที่เป็น JSON ให้ parse
      if (message.text.startsWith("{") && message.text.includes("text")) {
        try {
          const parsed = JSON.parse(message.text);
          text = parsed.text || message.text;
        } catch (error) {
          text = message.text;
        }
      } else {
        // ใช้ text โดยตรง (สำหรับ tempMessages)
        text = message.text;
      }
    } else {
      text = message.text || "";
    }

    const isUser = type === "user";

    console.log("Rendering message:", {
      type,
      text,
      isUser,
      originalText: message.text,
      textLength: text.length
    });

    return (
      <View
        style={{
          flexDirection: "row",
          justifyContent: isUser ? "flex-end" : "flex-start",
          marginVertical: 6,
          paddingHorizontal: 12,
        }}
      >
        <View
          style={{
            maxWidth: "70%",
            backgroundColor: isUser ? "#DCF8C6" : "#E8E8E8",
            padding: 10,
            borderRadius: 12,
            borderBottomRightRadius: isUser ? 0 : 12,
            borderBottomLeftRadius: isUser ? 12 : 0,
          }}
        >
          <Text style={[fonstStyle.f12_light, { color: isUser ? '#000' : '#000' }]}>
            {text || "ข้อความว่าง"}
          </Text>
        </View>
      </View>
    );
  };

  const renderKeyBord = () => {
    return (
      <View
        style={[
          ctn.ctn_chat,
          {
            paddingVertical: isFocused ? moderateScale(10) : moderateScale(10),
            paddingBottom: isFocused ? moderateScale(5) : moderateScale(30),
          },
        ]}
      >
        <TouchableOpacity onPress={openCamera}>{iconCamera()}</TouchableOpacity>
        <View style={{ margin: 5 }} />
        <TouchableOpacity onPress={selectImageFromLibrary}>
          {iconGalleryChat()}
        </TouchableOpacity>
        <View style={{ margin: 5 }} />
        <TouchableOpacity onPress={selectVideoFromLibrary}>
          {iconVideo()}
        </TouchableOpacity>
        <View style={{ margin: 5 }} />

        <TextInput
          style={[styles.cardInputKeyBordChat, fonstStyle.f12_light]}
          ref={inputRef}
          placeholder={type === "Admin" ? t("adminChat") : t("aiChat")}
          onChangeText={setMessage}
          value={message}
          editable
          multiline
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />

        <TouchableOpacity
          style={ctn.ctn_Send}
          disabled={!message}
          onPress={type === "Admin" ? callAdminMessage : callAiMessage}
        >
          {message ? iconSend() : iconSendDisble()}
        </TouchableOpacity>
      </View>
    );
  };
  const content = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <View style={ctn.continue}>
        {type === "Admin" ? (
          <FlatList
            data={[...adminLoadMessage, ...adminMessage]
              .filter(
                (msg, idx, arr) => arr.findIndex((m) => m.id === msg.id) === idx
              )
              .sort((a: any, b: any) =>
                a.messageTime.localeCompare(b.messageTime)
              )}
            renderItem={renderChat}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ paddingBottom: moderateScale(120) }}
            showsVerticalScrollIndicator={false}
            removeClippedSubviews={false}
            onContentSizeChange={() => {
              if (flatListRef.current) {
                flatListRef.current.scrollToEnd({ animated: true });
              }
            }}
          />
        ) : (
          <>
            <FlatList
              ref={flatListRef}
              data={[...tempMessages, ...aiLoadMessage]
                .filter(
                  (msg, idx, arr) =>
                    arr.findIndex((m) => m.id === msg.id) === idx
                )
                .sort((a: any, b: any) =>
                  a.created_at.localeCompare(b.created_at)
                )}
              renderItem={renderAi}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={false}
              contentContainerStyle={{ paddingBottom: moderateScale(120) }}
              onContentSizeChange={() => {
                if (flatListRef.current) {
                  flatListRef.current.scrollToEnd({ animated: true });
                }
              }}
            />
            {/* แสดง loading เมื่อรอการตอบกลับจาก AI */}
            {isAiLoading && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-start",
                  marginVertical: 6,
                  paddingHorizontal: 12,
                }}
              >
                <View
                  style={{
                    maxWidth: "70%",
                    backgroundColor: "#E8E8E8",
                    padding: 10,
                    borderRadius: 12,
                    borderBottomLeftRadius: 0,
                  }}
                >
                  <Text style={[fonstStyle.f12_light, { color: "#666" }]}>
                    กำลังพิมพ์...
                  </Text>
                </View>
              </View>
            )}
          </>
        )}
      </View>
      {renderKeyBord()}
    </KeyboardAvoidingView>
  );

  return (
    <>
      <StatusBar barStyle={"dark-content"} hidden={false} />
      <Default_Bar
        onBack={goBack}
        title={type === "Admin" ? t("adminChat") : t("aiChat")}
        imagesIcon={type === "Admin" ? Images.chatService : Images.chatAi}
      />
      {content()}
      {isModalFullImg === true && (
        <ModalFullImg
          images={[{ uri: imageFull }]}
          visible={isModalFullImg}
          onClose={() => setModalFullImg(false)}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  cardInputKeyBordChat: {
    width: "70%",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: BgColor.Bg_F4F4F4,
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  bubbleContainer: {
    // flexDirection: "row",
    alignItems: "flex-end",
    marginVertical: 6,
    paddingHorizontal: 10,
  },
  bubbleLeft: {
    justifyContent: "flex-start",
  },
  bubbleRight: {
    justifyContent: "flex-end",
    alignSelf: "flex-end",
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginHorizontal: 6,
  },
  bubble: {
    maxWidth: "70%",
    borderRadius: 18,
    padding: 10,
    marginHorizontal: 4,
  },
  bubbleMe: {
    backgroundColor: "#DCF8C6", // สีเขียวอ่อนแบบ Line
    borderTopRightRadius: 4,
  },
  bubbleOther: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 4,
    borderWidth: 1,
    borderColor: "#eee",
  },
  textMe: {
    color: "#222",
    fontSize: 16,
  },
  textOther: {
    color: "#222",
    fontSize: 16,
  },
  image: {
    width: 250,
    height: 180,
    borderRadius: 12,
    marginBottom: 4,
  },
  video: {
    width: 250,
    height: 180,
    borderRadius: 12,
    marginBottom: 4,
    overflow: "hidden",
  },
});
