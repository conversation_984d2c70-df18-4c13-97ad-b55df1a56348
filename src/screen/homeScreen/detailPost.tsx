import {
  Text,
  View,
  Modal,
  Alert,
  Animated,
  FlatList,
  Platform,
  StatusBar,
  TextInput,
  Dimensions,
  SafeAreaView,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
} from "react-native";
import moment from "moment";
import Video from "react-native-video";
import Share from "react-native-share";
import * as Progress from "react-native-progress";
import { Header as HeaderRNE } from "@rneui/themed";
import ImageViewing from "react-native-image-viewing";
import NetInfo from "@react-native-community/netinfo";
import { Volume2, VolumeX } from "lucide-react-native";
import ActionSheet, { ActionSheetRef } from "react-native-actions-sheet";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { verticalScale, moderateScale } from "react-native-size-matters";
import {
  LogLevel,
  HubConnection,
  HubConnectionBuilder,
} from "@microsoft/signalr";
//Style
import btn from "../../styleSheet/btn";
import ctn from "../../styleSheet/ctn";
import img from "../../styleSheet/img";
import txt from "../../styleSheet/txt";
import oth from "../../styleSheet/oth";
import mod from "../../styleSheet/mod";
import fonstStyle, { BgColor } from "../../styleSheet/style_Custom";
//Svg
import {
  iconLike,
  iconSend,
  iconLikeBg,
  iconComment,
  iconDotMenu,
  iconEditPost,
  iconDeletePost,
  iconDeleteNoti,
  iconSendDisble,
  iconForwardPost,
  iconForwardPostBg,
} from "../../assets/svg/svg_other";
import { goBack_gay } from "../../assets/svg/svg_naviagte";
//Api
import {
  shareSocial,
  commentApi,
  reCommentApi,
  deletePostApi,
  upDateReplyApi,
  upDateCommentApi,
  deleteCommentApi,
  deleteReplieComment,
} from "../../action/Mefarm_Social_API";
import { WEBSOCKET_ROOT } from "../../constants/api";
//Translation
import { useTranslation } from "../i18n";
//NumText
const NUM_OF_LINES = 5;
//Redux
import { useDispatch, useSelector } from "react-redux";
import {
  setDocListPost,
  updatePostLikes,
  setDocListFollow,
} from "../../Redux_Store/action";
import { useAuthTokens } from "../../hooks/useAuthTokens";
import { useOrientation } from "../../hooks/useOrientation";
import { setupNetworkListener } from "../../utils/networkManager";
import {
  joinSignalRRoom,
  setupSignalRConnection,
} from "../../utils/signalRManager";
import { MyImageComponent } from "../../components/cacheFiles/cache";
import { CachedVideo } from "../../components/cacheFiles/cacheVideo";

export default function DetailPost({ navigation, route }: any) {
  const orientation = useOrientation();
  const params = route.params || "";
  const docList = params.docList || "";
  const seePost = params.seePost || "";
  // const navigateId = params.navigateId || "";
  // console.log("navigateId", navigateId);

  const { accessToken, userId } = useAuthTokens();
  //Dispatch
  const dispatch = useDispatch();
  const docListPost = useSelector((state: any) => state.docListPost);
  const [isShowFullText, setIsShowFullText] = useState<boolean[]>(
    Array.isArray(docListPost) && docListPost.length > 0
      ? docListPost.map(() => false)
      : []
  );
  //State
  const { t } = useTranslation();
  //Array
  const [imageFull, setImageFull] = useState<any>([]);
  //String
  const [isComment, setComment] = useState<string>("");
  const [isReComment, setReComment] = useState<string>("");
  const [isCommentId, setCommentId] = useState<String>("");
  const [deleteCommentId, setDeleteCommentId] = useState<String>("");
  const [editTextComment, setEditTextcomment] = useState<any>("");
  const [socialCommentId, setSocialCommentId] = useState<any>("");
  //True & False
  const [showHeader, setShowHeader] = useState(true);
  const [isMuted, setIsMuted] = useState<boolean>(true);
  const [isLoadIng, setLoadIng] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isModalDelete, setModalDelete] = useState<boolean>(false);
  const [isModalFullImg, setModalFullImg] = useState<boolean>(false);
  const [isModalDeleteReply, setModalDeleteRePly] = useState<boolean>(false);
  const [isLoadLikes, setLoadLikes] = useState<{ [key: number]: boolean }>({});
  const [isLoadShare, setLoadShare] = useState<{ [key: number]: boolean }>({});
  //Null
  const inputRef: any = useRef(null);
  const connectionRef = useRef<any>(null);
  const [isReply, setReply] = useState<any>(null);
  const [editComment, setEditComment] = useState<any>(null);
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [imageDimensions, setImageDimensions] = useState<any>(null);
  const [connection, setConnection] = useState<null | HubConnection>(null);
  //Number
  const [progress, setProgress] = useState<number>(0);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const scrollY = useRef(new Animated.Value(0)).current;

  const actionSheetRef = useRef<ActionSheetRef>(null);
  const openActionSheet = () => {
    actionSheetRef.current?.show();
  };
  const closeActionSheet = () => {
    actionSheetRef.current?.hide();
  };

  useEffect(() => {
    const listener = scrollY.addListener(({ value }) => {
      setShowHeader(value < 100);
    });

    return () => scrollY.removeListener(listener);
  }, []);
  useEffect(() => {
    inputRef.current?.focus();
  }, []);
  useEffect(() => {
    let cleanup: (() => void) | null = null;

    const initConnection = async () => {
      try {
        const result = await setupSignalRConnection(
          dispatch,
          (newConnection) => {
            setConnection(newConnection);
            connectionRef.current = newConnection;
            joinRoom(newConnection); // ส่ง connection ที่เชื่อมต่อสำเร็จเข้าไป
          },
          { page: "home", forceReconnect: true } // เพิ่ม forceReconnect ถ้าต้องการเชื่อมใหม่ทุกครั้ง
        );
        cleanup = result.cleanup;
      } catch (error) {
        console.error("Error setting up SignalR connection:", error);
      }
    };

    initConnection();

    return () => {
      if (cleanup) cleanup();
    };
  }, [dispatch, userId, accessToken, pageSize]);

  const joinRoom = async (conn?: HubConnection) => {
    const activeConnection = conn || connectionRef.current;
    if (!activeConnection || activeConnection.state !== "Connected") return;

    const joinData = {
      roomId: "dashboard",
      followerUserId: userId,
      accessToken: accessToken,
      followingUserId: null,
      isDescending: true,
      firstPageSize: pageSize,
      sendType: "normal",
    };
    try {
      await activeConnection.invoke("JoinRoom", joinData);
    } catch (err) {
      console.error("Error invoking JoinRoom:", err);
    }
  };
  useEffect(() => {
    const unsubscribe = setupNetworkListener(
      isFocused,
      connection,
      joinRoom,
      navigation
    );

    return () => {
      unsubscribe();
      // console.log("Network listener cleaned up.");
    };
  }, [isFocused, connection]);
  const handleButtonPress = (value: string, comment: any) => {
    const commentId = comment.id || "";
    console.log(commentId);
    if (inputRef.current) {
      inputRef.current.focus();
    }
    setReply(value);
    setCommentId(commentId);
    console.log(value);
  };
  const handleButtonEdit = (value: string, comment: any) => {
    const commentId = comment.id || "";
    setEditComment(value);
    setCommentId(commentId);
  };
  const handleButtonRePly = (value: string, reply: any) => {
    const commentId = reply.id || "";
    const socialCommentId = reply.socialPostCommentId || "";
    setEditComment(value);
    setCommentId(commentId);
    setSocialCommentId(socialCommentId);
    console.log(socialCommentId);
  };
  const toggleFullText = (index: number) => {
    setIsShowFullText((x) => {
      const nextState = x ? [...x] : [];
      nextState[index] = !nextState[index];
      return nextState;
    });
  };
  const onDelete = async (item: any, indexItem: number) => {
    try {
      setLoadIng(true);
      const socialPostId = item.id || "";
      let data = await deletePostApi(socialPostId);

      if (data.success) {
        let deleteData = docListPost;
        if (data.model.id == deleteData[indexItem].id) {
          deleteData.splice(indexItem, 1);
        }

        setDocListPost(deleteData);
        goBack();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadIng(false);
      setOpenIndex(null);
      // joinRoom();
    }
  };
  const onShare = async (item: any, index: number) => {
    try {
      setLoadShare((prevState) => ({ ...prevState, [index]: true }));
      // console.log(JSON.stringify(item, null, 2));

      const req = await shareSocial(item.id);
      const data = req.model.sharedUrl || "";
      // console.log(req);
      const result = await Share.open({
        title: "Mefarm",
        message: "",
        url: data,
      });
      console.log("แชร์สำเร็จ:", result);
    } catch (error: any) {
      if (error.message !== "User did not share") {
        Alert.alert("แชร์ไม่สำเร็จ", error.message);
      }
    } finally {
      joinRoom();
      setLoadShare((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const onComment = async () => {
    setComment("");
    setProgress(0);
    try {
      setLoadIng(true);
      const req = {
        id: userId,
        socialPostId: docList.id,
        messageContent: isComment,
        deleteFileIds: [],
        imageContent: [],
        videoContent: [],
      };

      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress = Math.min(currentProgress + 0.1, 1); // ค่าไม่ให้ 1
        setProgress(currentProgress);

        if (currentProgress >= 1) clearInterval(interval);
      }, 200);

      let res = await commentApi(req);

      clearInterval(interval); // ปเดต Progress
      setProgress(1); // ตั้งค่าเป็น 100% เมื่อเสร็จ

      // console.log("data....", res);
    } catch (error) {
      console.log("Error...", error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0); // เซ็ต Progress เมื่อเสร็
        joinRoom();
      }, 500);
    }
  };
  const onReComment = async () => {
    setReComment("");
    setProgress(0);
    try {
      setLoadIng(true);
      const req = {
        id: userId,
        socialPostId: docList.id,
        socialPostCommentId: isCommentId,
        messageContent: isReComment,
        deleteFileIds: [],
        imageContent: [],
        videoContent: [],
      };
      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress = Math.min(currentProgress + 0.1, 1); // ค่าไม่ให้ 1
        setProgress(currentProgress);

        if (currentProgress >= 1) clearInterval(interval);
      }, 200);

      let data = await reCommentApi(req);

      clearInterval(interval);
      setProgress(1);
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0);
        joinRoom();
      }, 500);
    }
  };
  const onDeleteComment = async () => {
    setProgress(0);
    try {
      setLoadIng(true);
      // const commentId = comment.id || "";

      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress += 0.1;
        setProgress(currentProgress);
        if (currentProgress >= 1) clearInterval(interval);
      }, 200);

      let data = await deleteCommentApi(deleteCommentId);

      clearInterval(interval);
      setProgress(1);
      setModalDelete(false);
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0);
        joinRoom();
      }, 500);
    }
  };
  const onDeleteReComment = async () => {
    try {
      setLoadIng(true);

      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress += 0.1;
        setProgress(currentProgress);
        if (currentProgress >= 1) clearInterval(interval);
      }, 200);
      let data = await deleteReplieComment(deleteCommentId);
      clearInterval(interval);
      setProgress(1);
      setModalDeleteRePly(false);
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0);
        joinRoom();
      }, 500);
    }
  };
  const onRefreshHandler = useCallback(async () => {
    setLoadIng(true);

    await joinRoom(); // โหลดข้อมูลใหม่
    setLoadIng(false);
  }, []);
  const onEditComment = async (comment: any) => {
    try {
      setLoadIng(true);
      const req = {
        id: isCommentId,
        socialPostId: comment.socialPostId,
        messageContent: editTextComment,
        deleteFileIds: [],
        imageContent: [],
        videoContent: [],
      };

      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress += 0.1;
        setProgress(currentProgress);
        if (currentProgress >= 1) clearInterval(interval);
      }, 200);

      let data = await upDateCommentApi(req);

      clearInterval(interval);
      setProgress(1);
      setEditTextcomment("");
      setEditComment("");
    } catch (error) {
      console.log("Error...", error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0);

        joinRoom();
      }, 500);
    }
  };
  const onEditRePly = async (comment: any) => {
    try {
      setLoadIng(true);
      const req = {
        id: isCommentId,
        socialPostId: comment.socialPostId,
        socialPostCommentId: socialCommentId,
        messageContent: editTextComment,
        deleteFileIds: [],
        imageContent: [],
        videoContent: [],
      };

      let currentProgress = 0;
      const interval = setInterval(() => {
        currentProgress += 0.1;
        setProgress(currentProgress);
        if (currentProgress >= 1) clearInterval(interval);
      }, 200);

      let data = await upDateReplyApi(req);

      clearInterval(interval);
      setProgress(1);
      setEditTextcomment("");
      setEditComment("");
    } catch (error) {
      console.log("Error...", error);
    } finally {
      setTimeout(() => {
        setLoadIng(false);
        setProgress(0);
        joinRoom();
      }, 500);
    }
  };
  const goLike = async (item: any, index: number) => {
    try {
      setLoadLikes((prevState) => ({ ...prevState, [index]: true }));
      if (connection) {
        await connection.invoke("LikePostMessage", item.id);
        dispatch(updatePostLikes(item.id));
      }
    } catch (error) {
      console.log(error);
    } finally {
      joinRoom();
      setLoadLikes((prevState) => ({ ...prevState, [index]: false }));
    }
  };
  const goFullImg = (item: any, imageIndex: number) => {
    setImageFull(item.imageContents);
    setCurrentIndex(imageIndex);
    setModalFullImg(true);
  };
  const goEditPost = (item: any) => {
    navigation.navigate("EditPost", {
      docList: item,
    });
    setOpenIndex(null);
  };
  const sendMessage = () => {
    if (isReply === null) {
      onComment();
    } else {
      onReComment();
    }
  };
  const goBack = () => {
    navigation.navigate("Bottom_Tab", {
      screen: "Home",
    });
  };
  const goFriend = (item: any) => {
    navigation.navigate("Profilefriend", { item: item });
  };
  const onOpenDelete = (comment: any) => {
    const commentId = comment.id || "";
    setDeleteCommentId(commentId);
    setModalDelete(true);
  };
  const onOpenDeleteReply = (comment: any) => {
    const res = comment.replies || "";
    const commentId = res.map((item: any) => item.id);
    setDeleteCommentId(commentId);
    setModalDeleteRePly(true);
  };
  const FlasListVertical = () => (
    <FlatList
      data={docListPost}
      scrollEnabled={true}
      renderItem={renderVertical}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={() => <View style={{ margin: moderateScale(20) }} />}
      refreshControl={
        <RefreshControl refreshing={isLoadIng} onRefresh={onRefreshHandler} />
      }
    />
  );

  //Ui
  const headerBar = () => (
    <HeaderRNE
      backgroundColor={BgColor.Bg_FFFFFF}
      leftComponent={lefHeader()}
      centerComponent={centerComment()}
    />
  );
  const lefHeader = () => (
    <TouchableOpacity onPress={() => goBack()}>{goBack_gay()}</TouchableOpacity>
  );
  const centerComment = () => (
    <Text
      style={[
        fonstStyle.f16_bold,
        txt.txt_616161,
        { marginTop: moderateScale(10) },
      ]}
    >
      {t("comment_post")}
    </Text>
  );
  const renderKeyBord = () => {
    return (
      <View style={ctn.ctn_keyboardcomment}>
        <View style={ctn.ctn_KeyBordComment}>
          <View
            style={[
              oth.cardKeyBord,
              {
                paddingVertical: isFocused
                  ? moderateScale(10)
                  : moderateScale(10),
                paddingBottom: isFocused ? moderateScale(5) : moderateScale(30),
              },
            ]}
          >
            <TextInput
              style={[oth.cardInputKeyBord, fonstStyle.f12_light]}
              ref={inputRef}
              placeholder="Write a comment..."
              onChangeText={isReply ? setReComment : setComment}
              value={isReply ? isReComment : isComment}
              autoFocus={seePost ? false : true}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              editable
              multiline
            />
            <View style={{ margin: moderateScale(2) }} />

            {isComment || isReComment ? (
              <TouchableOpacity
                style={ctn.ctn_Send}
                onPress={() => sendMessage()}
              >
                {iconSend()}
              </TouchableOpacity>
            ) : (
              <View style={ctn.ctn_Send}>{iconSendDisble()}</View>
            )}
          </View>
        </View>
      </View>
    );
  };
  const renderVertical = ({ item, index }: any) => {
    const isTextLongEnough =
      item && item.messageContent && item.messageContent.length > 200;

    return (
      <>
        {docList.id === item?.id ? (
          <View style={[oth.card_Vartical]}>
            <View style={{ flexDirection: "row", padding: moderateScale(10) }}>
              <TouchableOpacity
                style={ctn.ctn_profileVtcal}
                onPress={() =>
                  item?.userId === userId
                    ? navigation.navigate("ProfileUser")
                    : goFriend(item)
                }
              >
                <MyImageComponent
                  imageUrl={item?.profileImageUrl}
                  style={img.img_profile}
                />
              </TouchableOpacity>
              <View style={ctn.ctn_TxtProfile}>
                <TouchableOpacity
                  onPress={() =>
                    item?.userId === userId
                      ? navigation.navigate("ProfileUser")
                      : goFriend(item)
                  }
                >
                  <Text
                    style={[txt.txt_Vtcal, fonstStyle.f14_bold, txt.txt_606060]}
                    numberOfLines={5}
                  >
                    {item?.firstName}
                  </Text>
                </TouchableOpacity>
                <Text
                  style={[txt.txt_time, fonstStyle.f12_light, txt.txt_606060]}
                >
                  {moment(item?.updatedAt).locale(t("locale")).fromNow()}
                </Text>
              </View>
            </View>

            <View style={ctn.ctn_MenuDot}>
              {item?.userId === userId ? (
                <TouchableOpacity onPress={() => openActionSheet()}>
                  {iconDotMenu()}
                </TouchableOpacity>
              ) : null}

              <ActionSheet
                ref={actionSheetRef}
                gestureEnabled
                containerStyle={{
                  borderTopLeftRadius: 20,
                  borderTopRightRadius: 20,
                }}
              >
                <TouchableOpacity
                  style={{ padding: 20 }}
                  onPress={() => {
                    closeActionSheet();
                    goEditPost(item);
                  }}
                >
                  <View
                    style={{ flexDirection: "row", justifyContent: "center" }}
                  >
                    {iconEditPost()}
                    <View style={{ margin: moderateScale(5) }} />

                    <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                      {t("editPost")}
                    </Text>
                  </View>
                </TouchableOpacity>
                <View style={oth.line_profile} />

                <TouchableOpacity
                  style={{ padding: 20 }}
                  // onPress={() => onDelete(item, index)}
                  onPress={() => {
                    closeActionSheet();
                    onDelete(item, index);
                  }}
                >
                  <View
                    style={{ flexDirection: "row", justifyContent: "center" }}
                  >
                    {iconDeletePost()}
                    <View style={{ margin: moderateScale(5) }} />

                    <Text style={[fonstStyle.f14_light, txt.txt_606060]}>
                      {t("delete_Post")}
                    </Text>
                  </View>
                </TouchableOpacity>
                <View style={oth.line_profile} />

                <TouchableOpacity
                  style={{ padding: 20 }}
                  onPress={closeActionSheet}
                >
                  <Text
                    style={[
                      fonstStyle.f14_light,
                      txt.txt_red,
                      { textAlign: "center" },
                    ]}
                  >
                    {t("cancel")}
                  </Text>
                </TouchableOpacity>
              </ActionSheet>
            </View>

            {item?.messageContent != "" ? (
              <Text
                numberOfLines={
                  !isShowFullText[index] ? NUM_OF_LINES : undefined
                }
                style={[
                  txt.txt_cardPost,
                  fonstStyle.f12_light,
                  txt.txt_606060,
                  { paddingHorizontal: moderateScale(10) },
                ]}
              >
                {item?.messageContent}
              </Text>
            ) : null}

            {/* {TextPost} */}
            {isTextLongEnough ? (
              <TouchableOpacity onPress={() => toggleFullText(index)}>
                <Text style={[txt.txt_fullTxt, fonstStyle.f10_bold]}>
                  {!isShowFullText[index] ? t("show_more") : t("close_more")}
                </Text>
              </TouchableOpacity>
            ) : null}
            <View style={{ margin: moderateScale(5) }} />

            {/* {ภาพ} */}
            {item?.imageContents.length > 0 && (
              <View style={ctn.ctn_imagePost}>
                {item?.imageContents.map((image: any, idx: number) => (
                  <TouchableOpacity
                    key={image.id}
                    onPress={() => goFullImg(item, idx)}
                    style={{
                      width: "100%", // ให้ภาพเต็มความกว้างของ Container
                      marginBottom: moderateScale(4), // เ่่มระยะห่างระหว่างภาพ
                    }}
                  >
                    <MyImageComponent
                      imageUrl={image.url}
                      style={[
                        img.img_imagePost,
                        imageDimensions && {
                          width: imageDimensions.width,
                          height: imageDimensions.height,
                        },
                      ]}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* {โอ} */}
            {item?.videoContents.length > 0 && (
              <View style={ctn.ctn_imagePost}>
                {item?.videoContents && item?.videoContents.length > 0 && (
                  <>
                    <View style={ctn.ctn_imagePost}>
                      <View style={{ width: "100%", alignItems: "center" }}>
                        <CachedVideo
                          videoUrl={item.videoContents[0]?.url}
                          style={ctn.ctn_video}
                        />
                      </View>
                    </View>
                  </>
                )}
              </View>
            )}

            {/* {like and comment} */}
            <View style={{ margin: moderateScale(5) }} />
            <View style={ctn.ctn_likeComment}>
              <TouchableOpacity
                onPress={() => goLike(item, index)}
                style={{ flexDirection: "row" }}
              >
                {isLoadLikes[index] ? (
                  <ActivityIndicator size="small" color={BgColor.Bg_D6D6D6} />
                ) : item?.isLike ? (
                  iconLikeBg()
                ) : (
                  iconLike()
                )}

                <View style={{ margin: moderateScale(2) }} />

                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {item?.numberOfLike || "0"}
                  </Text>
                  <View style={{ margin: moderateScale(2) }} />
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {t("like_post")}
                  </Text>
                </View>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(2) }} />

              <TouchableOpacity style={{ flexDirection: "row" }}>
                <View style={{ margin: moderateScale(5) }} />
                {iconComment()}
                <View style={{ margin: moderateScale(2) }} />

                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {item?.comments.length || "0"}
                  </Text>
                  <View style={{ margin: moderateScale(2) }} />
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {t("comment_post")}
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => onShare(item, index)}
                style={{ flexDirection: "row" }}
              >
                <View style={{ margin: moderateScale(5) }} />

                {isLoadShare[index] ? (
                  <ActivityIndicator size="small" color={BgColor.Bg_D6D6D6} />
                ) : item?.isShare ? (
                  iconForwardPostBg()
                ) : (
                  iconForwardPost()
                )}

                <View style={{ margin: moderateScale(2) }} />

                <View style={{ flexDirection: "row" }}>
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {item?.numberOfShare || "0"}
                  </Text>
                  <View style={{ margin: moderateScale(2) }} />
                  <Text style={[fonstStyle.f12_bold, txt.txt_likeComment]}>
                    {t("share")}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <View style={{ margin: moderateScale(14) }} />

            {/* Comment people */}
            {item.comments.map((comment: any) => (
              <>
                <View key={comment.id} style={ctn.ctn_comment}>
                  <TouchableOpacity
                    style={[ctn.ctn_profileComment]}
                    onPress={() =>
                      comment?.userId === userId ? undefined : goFriend(comment)
                    }
                  >
                    <MyImageComponent
                      imageUrl={comment.profileImageUrl}
                      style={img.img_profileComment}
                    />
                  </TouchableOpacity>
                  <View style={{ margin: moderateScale(2) }} />
                  <View style={ctn.ctn_nameComtent}>
                    <TouchableOpacity
                      onPress={() =>
                        comment?.userId === userId
                          ? undefined
                          : goFriend(comment)
                      }
                    >
                      <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                        {comment.firstName}
                      </Text>
                    </TouchableOpacity>
                    <View style={{ margin: moderateScale(2) }} />

                    {/* {Edit comment} */}
                    {comment.userId !== userId ||
                    isCommentId !== comment.id ||
                    !editComment ? (
                      <Text style={[fonstStyle.f12_light, txt.txt_606060]}>
                        {comment.messageContent}
                      </Text>
                    ) : (
                      <View style={{ width: "100%" }}>
                        <TextInput
                          style={[txt.txt_editComment, fonstStyle.f12_light]}
                          ref={inputRef}
                          placeholder={comment.messageContent}
                          onChangeText={setEditTextcomment}
                          value={editTextComment}
                          editable
                          multiline
                        />

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "flex-end",
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => setEditComment("")}
                            style={oth.bottonCanCel_Comment}
                          >
                            <Text style={[fonstStyle.f10_bold, txt.txt_606060]}>
                              {t("replies_cancle")}
                            </Text>
                          </TouchableOpacity>
                          <View style={{ margin: moderateScale(5) }} />

                          {editTextComment && (
                            <TouchableOpacity
                              style={oth.bottonSave_Comment}
                              onPress={() => onEditComment(comment)}
                            >
                              <Text
                                style={[fonstStyle.f10_bold, txt.txt_editSave]}
                              >
                                {t("replies_save")}
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    )}
                  </View>
                </View>

                {/* {ตอบ ลบ แก้ไข} */}
                <View style={ctn.ctn_optionComment}>
                  <Text style={[fonstStyle.f10_light, txt.txt_dateOption]}>
                    {moment(comment.updatedAt).locale(t("locale")).fromNow()}
                  </Text>
                  <View style={{ margin: moderateScale(5) }} />
                  {comment.userId === userId && (
                    <>
                      <TouchableOpacity
                        // onPress={() => onDeleteComment(comment)}
                        onPress={() => onOpenDelete(comment)}
                      >
                        <Text
                          style={[fonstStyle.f10_light, txt.txt_deleteOption]}
                        >
                          {t("delete_comment")}
                        </Text>
                      </TouchableOpacity>
                      <View style={{ margin: moderateScale(5) }} />
                    </>
                  )}
                  {comment.userId === userId && (
                    <>
                      <TouchableOpacity
                        onPress={() => handleButtonEdit("editComment", comment)}
                      >
                        <Text
                          style={[fonstStyle.f10_light, txt.txt_editOption]}
                        >
                          {t("edit_comment")}
                        </Text>
                      </TouchableOpacity>
                      <View style={{ margin: moderateScale(5) }} />
                    </>
                  )}
                  <TouchableOpacity
                    onPress={() => handleButtonPress("replyComment", comment)}
                  >
                    <Text style={[fonstStyle.f10_light, txt.txt_repayOption]}>
                      {t("reply")}
                    </Text>
                  </TouchableOpacity>
                </View>
                <View style={{ margin: moderateScale(5) }} />

                {/* {Box Reply ข้อความตอบ} */}
                {comment.replies.map((reply: any) => (
                  <>
                    <View style={ctn.ctn_reComment}>
                      <TouchableOpacity
                        style={ctn.ctn_profileComment}
                        onPress={() =>
                          reply?.userId === userId ? undefined : goFriend(reply)
                        }
                      >
                        <MyImageComponent
                          imageUrl={reply.profileImageUrl}
                          style={img.img_profileComment}
                        />
                      </TouchableOpacity>
                      <View style={{ margin: moderateScale(2) }} />
                      <View style={ctn.ctn_nameComtent}>
                        <TouchableOpacity
                          onPress={() =>
                            reply?.userId === userId
                              ? undefined
                              : goFriend(reply)
                          }
                        >
                          <Text style={[fonstStyle.f12_bold, txt.txt_606060]}>
                            {reply.firstName}
                          </Text>
                        </TouchableOpacity>
                        <View style={{ margin: moderateScale(2) }} />

                        {reply.userId !== userId ||
                        isCommentId !== reply.id ||
                        !editComment ? (
                          <Text
                            key={reply.id}
                            style={[fonstStyle.f12_light, txt.txt_606060]}
                          >
                            {reply.messageContent}
                          </Text>
                        ) : (
                          <View style={{ width: "100%" }}>
                            <TextInput
                              style={[
                                txt.txt_editComment,
                                fonstStyle.f12_light,
                              ]}
                              ref={inputRef}
                              placeholder={reply.messageContent}
                              onChangeText={setEditTextcomment}
                              value={editTextComment}
                              editable
                              multiline
                            />

                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "flex-end",
                              }}
                            >
                              <TouchableOpacity
                                onPress={() => setEditComment("")}
                                style={oth.bottonCanCel_Comment}
                              >
                                <Text style={[fonstStyle.f10_light]}>
                                  {t("replies_cancle")}
                                </Text>
                              </TouchableOpacity>
                              <View style={{ margin: moderateScale(5) }} />

                              {editTextComment && (
                                <TouchableOpacity
                                  style={oth.bottonSave_Comment}
                                  onPress={() => onEditRePly(comment)}
                                >
                                  <Text
                                    style={[
                                      fonstStyle.f10_light,
                                      txt.txt_editSave,
                                    ]}
                                  >
                                    {t("replies_save")}
                                  </Text>
                                </TouchableOpacity>
                              )}
                            </View>
                          </View>
                        )}
                      </View>
                    </View>

                    {/* {Reply ตอบ ลบ แก้ไข} */}
                    <View style={ctn.ctn_optionReComment}>
                      <Text style={[fonstStyle.f10_light, txt.txt_dateOption]}>
                        {moment(reply.updatedAt).locale(t("locale")).fromNow()}
                      </Text>
                      <View style={{ margin: moderateScale(5) }} />
                      {reply.userId === userId && (
                        <>
                          <TouchableOpacity
                            // onPress={() => onDeleteReComment(comment)}
                            onPress={() => onOpenDeleteReply(comment)}
                          >
                            <Text
                              style={[
                                fonstStyle.f10_light,
                                txt.txt_deleteOption,
                              ]}
                            >
                              {t("delete_comment")}
                            </Text>
                          </TouchableOpacity>
                          <View style={{ margin: moderateScale(5) }} />
                        </>
                      )}
                      {reply.userId === userId && (
                        <>
                          <TouchableOpacity
                            onPress={() =>
                              handleButtonRePly("editRePly", reply)
                            }
                          >
                            <Text
                              style={[
                                fonstStyle.f10_light,
                                txt.txt_repayOption,
                              ]}
                            >
                              {t("edit_comment")}
                            </Text>
                          </TouchableOpacity>
                          <View style={{ margin: moderateScale(5) }} />
                        </>
                      )}
                    </View>
                    <View style={{ margin: moderateScale(5) }} />
                  </>
                ))}
              </>
            ))}
          </View>
        ) : null}
      </>
    );
  };
  const modalFullImg = () => {
    return (
      <ImageViewing
        images={imageFull.map((img: any) => ({ uri: img.url }))}
        imageIndex={currentIndex}
        visible={isModalFullImg}
        onRequestClose={() => setModalFullImg(false)}
        FooterComponent={({ imageIndex }) => (
          <View style={ctn.ctn_fullImages}>
            <Text style={[fonstStyle.f12_light, txt.txt_white]}>
              {`${imageIndex + 1} / ${imageFull.length}`}
            </Text>
          </View>
        )}
      />
    );
  };
  const modalDelete = () => {
    return (
      <Modal animationType="fade" transparent={true} visible={isModalDelete}>
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconDeleteNoti()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                {t("delete_comment_Post")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => setModalDelete(false)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonDelete}
                onPress={() => onDeleteComment()}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  const modalDeleteReply = () => {
    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={isModalDeleteReply}
      >
        <View style={mod.mod_center}>
          <View style={mod.mod_View}>
            <View style={oth.opt_FlaseCancle}>
              <View style={oth.bg_FlaseCancle}>{iconDeleteNoti()}</View>
            </View>
            <View style={{ bottom: verticalScale(30) }}>
              <Text style={[txt.txt_modFlase, fonstStyle.f14_bold]}>
                {t("delete_comment_Post")}
              </Text>
            </View>
            <View style={{ flexDirection: "row" }}>
              <TouchableOpacity
                style={btn.btn_bottonCancle}
                onPress={() => setModalDeleteRePly(false)}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_orange]}>
                  {t("cancel")}
                </Text>
              </TouchableOpacity>
              <View style={{ margin: moderateScale(5) }} />

              <TouchableOpacity
                style={btn.btn_bottonDelete}
                onPress={() => onDeleteReComment()}
              >
                <Text style={[fonstStyle.f12_bold, txt.txt_white]}>
                  {t("confirm")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  //Main
  const mainPortrait = () => {
    return (
      <>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
        >
          <View style={ctn.continueMain}>
            {isLoadIng && (
              <View style={{ width: "100%" }}>
                <Progress.Bar
                  progress={progress / 100}
                  width={null}
                  style={{ width: "100%" }}
                  color={BgColor.Bg_6A938D}
                  borderRadius={0}
                  borderWidth={0}
                />
              </View>
            )}
            {FlasListVertical()}
          </View>
          {renderKeyBord()}
          {modalFullImg()}
          {modalDelete()}
          {modalDeleteReply()}
        </KeyboardAvoidingView>
      </>
    );
  };
  const mainlandscape = () => {
    return (
      <SafeAreaView style={[ctn.continue]}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
        >
          <View style={ctn.continueMain}>
            {isLoadIng && (
              <View style={{ width: "100%" }}>
                <Progress.Bar
                  progress={progress / 100}
                  width={null}
                  style={{ width: "100%" }}
                  color={BgColor.Bg_6A938D}
                  borderRadius={0}
                  borderWidth={0}
                />
              </View>
            )}
            {FlasListVertical()}
          </View>
          {renderKeyBord()}
          {modalFullImg()}
          {modalDelete()}
          {modalDeleteReply()}
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  };

  return (
    <>
      <StatusBar barStyle="dark-content" hidden={false} />
      {headerBar()}
      {orientation === "portrait" ? mainPortrait() : mainlandscape()}
    </>
  );
}
