import EditPoltname from "../components/manageFarm/editPoltname";

const Images = {
  gps: require("../assets/Images/control/gps.png"),
  bgApp: require("../assets/Images/logo/bgApp.png"),
  Apple: require("../assets/Images/soci/apple.png"),
  bell: require("../assets/Images/control/bell.png"),
  info: require("../assets/Images/control/info.png"),
  cctv: require("../assets/Images/control/cctv.png"),
  chat: require("../assets/Images/control/chat.png"),
  save: require("../assets/Images/control/save.png"),
  list: require("../assets/Images/control/list.png"),
  GooGle: require("../assets/Images/soci/google.png"),
  menu1: require("../assets/Images/control/menu1.png"),
  menu2: require("../assets/Images/control/menu2.png"),
  menu3: require("../assets/Images/control/menu3.png"),
  menu4: require("../assets/Images/control/menu4.png"),
  pktop: require("../assets/Images/control/pktop.png"),
  theSun: require("../assets/Images/control/theSun.png"),
  pkdown: require("../assets/Images/control/pkdown.png"),
  Mapnon: require("../assets/Images/control/map-non.png"),
  chatAi: require("../assets/Images/control/chat-ai.png"),
  FaceBook: require("../assets/Images/soci/facebook.png"),
  phCheck: require("../assets/Images/control/phCheck.png"),
  Ellipse: require("../assets/Images/control/Ellipse.png"),
  picture: require("../assets/Images/control/picture.png"),
  Drawmap: require("../assets/Images/control/drawMap.png"),
  Control1: require("../assets/Images/control/control1.png"),
  Control2: require("../assets/Images/control/control2.png"),
  Control3: require("../assets/Images/control/control3.png"),
  Control4: require("../assets/Images/control/control4.png"),
  editPolt: require("../assets/Images/control/editPlot.png"),
  textMefarm: require("../assets/Images/logo/textMefarm.png"),
  promptpay: require("../assets/Images/package/promptpay.png"),
  SmartFarm: require("../assets/Images/control/SmartFarm.png"),
  finishStatus: require("../assets/Images/control/finish.png"),
  imgOnArea: require("../assets/Images/package/imgOnArea.png"),
  Mapselect: require("../assets/Images/control/map-select.png"),
  vegetables: require("../assets/Images/control/vegetables.png"),
  outOfStock: require("../assets/Images/control/outOfStock.png"),
  waterCheck: require("../assets/Images/control/waterCheck.png"),
  Removerubbish: require("../assets/Images/control/rubbish.png"),
  LogoMeFarmHug: require("../assets/Images/logo/RabbitLogo.png"),
  gifwatering: require("../assets/Images/control/gifwatering.gif"),
  chatService: require("../assets/Images/control/chat-service.png"),
  ControlClean: require("../assets/Images/control/controlClean.png"),
  wateringList: require("../assets/Images/control/wateringList.png"),
  textStartMefarm: require("../assets/Images/logo/textStartMefarm.png"),
  TransparentWoo: require("../assets/Images/control/TransparentWoo.png"),
  historyDelivery: require("../assets/Images/control/historyDelivery.png"),
  TransparentWooFFF: require("../assets/Images/control/TransparentWooFFF.png"),
};

export default Images;
