import {
  <PERSON>g<PERSON><PERSON>l,
  Hu<PERSON><PERSON>onnection,
  HubConnectionBuilder,
  HubConnectionState,
} from "@microsoft/signalr";
import { Dispatch } from "redux";
import {
  addNewPost,
  setDocProfile,
  setDocListPost,
  updatePostLikes,
  setFollowConter,
  setDocListFollow,
  setDocListProfile,
  setDocListFriend,
  updateMemberFollowStatus,
} from "../Redux_Store/action";
import { WEBSOCKET_ROOT } from "../constants/api";

// Singleton connection & cleanup
let globalConnection: HubConnection | null = null;
let globalCleanup: (() => void) | null = null;

export const setupSignalRConnection = async (
  dispatch: Dispatch<any>,
  onConnectionEstablished: (connection: HubConnection) => void,
  options: {
    page?: "home" | "profile" | "friend" | "all";
    onFollowCounterReceived?: (data: any) => void;
    forceReconnect?: boolean;
  } = {}
): Promise<{ connection: HubConnection; cleanup: () => void }> => {
  const { page = "all", onFollowCounterReceived, forceReconnect = false } = options;

  // Reuse connection if exists and not forceReconnect
  if (
    !forceReconnect &&
    globalConnection &&
    globalConnection.state === HubConnectionState.Connected
  ) {
    onConnectionEstablished(globalConnection);
    return { connection: globalConnection, cleanup: globalCleanup! };
  }

  // If forceReconnect or no connection, cleanup old one
  if (globalCleanup) {
    globalCleanup();
  }

  // Create new connection
  const newConnection = new HubConnectionBuilder()
    .withUrl(WEBSOCKET_ROOT)
    .withAutomaticReconnect()
    .configureLogging(LogLevel.Information)
    .build();

  // Event Listeners
  newConnection.on("ReceiveLoadMoreMemberMessage", (data) => {
    if (data) dispatch(setDocListFollow(data));
  });

  newConnection.on("ReceiveUpdateMemberMessage", (data) => {
    dispatch(updateMemberFollowStatus(data.userId, data.isFollowing));
  });

  newConnection.on("ReceiveLoadMorePostMessage", (data) => {
    if (!data) return;
    if (page === "home") dispatch(setDocListPost(data));
    if (page === "profile") dispatch(setDocListProfile(data));
    if (page === "friend") dispatch(setDocListFriend(data));
  });

  newConnection.on("ReceiveUpdatePostMessage", (data) => {
    dispatch(updatePostLikes(data.id, data.likes));
  });

  newConnection.on("ReceiveNewPostMessage", (data) => {
    if (!data) return;
    if (page === "home") dispatch(addNewPost(data));
    if (page === "profile")
      dispatch(setDocListProfile((prevPosts: any) => [data, ...prevPosts]));
    if (page === "friend")
      dispatch(setDocListFriend((prevPosts: any) => [data, ...prevPosts]));
  });

  newConnection.on("ReceiveFollowCounterMessage", (data: any) => {
    if (data) dispatch(setFollowConter(data));
    if (onFollowCounterReceived) onFollowCounterReceived(data);
  });

  newConnection.on("ReceiveProfileImageMessage", (data: any) => {
    if (data) dispatch(setDocProfile(data));
  });

  // Handle connection error/reconnect
  newConnection.onclose((error) => {
    console.warn("SignalR connection closed", error);
    // Optionally: แจ้งเตือน user หรือพยายาม reconnect เพิ่มเติม
  });

  newConnection.onreconnecting((error) => {
    console.warn("SignalR reconnecting...", error);
  });

  newConnection.onreconnected((connectionId) => {
    console.log("SignalR reconnected", connectionId);
  });

  try {
    await newConnection.start();
    console.log("SignalR connection established.");

    onConnectionEstablished(newConnection);

    const cleanup = () => {
      newConnection.off("ReceiveLoadMoreMemberMessage");
      newConnection.off("ReceiveUpdateMemberMessage");
      newConnection.off("ReceiveLoadMorePostMessage");
      newConnection.off("ReceiveUpdatePostMessage");
      newConnection.off("ReceiveNewPostMessage");
      newConnection.off("ReceiveFollowCounterMessage");
      newConnection.off("ReceiveProfileImageMessage");
      newConnection.stop().catch(() => {});
      globalConnection = null;
      globalCleanup = null;
    };

    globalConnection = newConnection;
    globalCleanup = cleanup;

    return { connection: newConnection, cleanup };
  } catch (err) {
    // cleanup ถ้า error
    if (globalCleanup) globalCleanup();
    throw err;
  }
};

// ฟังก์ชันสำหรับเข้าร่วมห้อง
export const joinSignalRRoom = async (
  connection: HubConnection,
  roomData: {
    roomId: string;
    followerUserId: string;
    accessToken: string;
    followingUserId: string | null;
    isDescending: boolean;
    firstPageSize: number;
    sendType: string;
  },
  onLoadingStart?: () => void,
  onLoadingEnd?: () => void
) => {
  if (!connection || connection.state !== "Connected") {
    return false;
  }

  try {
    if (onLoadingStart) onLoadingStart();
    await connection.invoke("JoinRoom", roomData);
    return true;
  } catch (err) {
    return false;
  } finally {
    if (onLoadingEnd) onLoadingEnd();
  }
};
